package net.minecraft.entity.player;

import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.mojang.authlib.GameProfile;
import de.florianmichael.viamcp.fixes.DropItemSwing;
import net.bloom.bloomclient.BloomClient;
import net.bloom.bloomclient.event.KnockbackEvent;
import net.bloom.bloomclient.features.component.components.player.RotationComponent;
import net.bloom.bloomclient.features.module.modules.combat.ModuleKillAura;
import net.minecraft.block.Block;
import net.minecraft.block.BlockBed;
import net.minecraft.block.BlockDirectional;
import net.minecraft.block.material.Material;
import net.minecraft.block.state.IBlockState;
import net.minecraft.client.Minecraft;
import net.minecraft.client.network.NetworkPlayerInfo;
import net.minecraft.client.resources.DefaultPlayerSkin;
import net.minecraft.command.server.CommandBlockLogic;
import net.minecraft.enchantment.EnchantmentHelper;
import net.minecraft.entity.*;
import net.minecraft.entity.ai.attributes.IAttributeInstance;
import net.minecraft.entity.boss.EntityDragonPart;
import net.minecraft.entity.item.EntityBoat;
import net.minecraft.entity.item.EntityItem;
import net.minecraft.entity.item.EntityMinecart;
import net.minecraft.entity.monster.EntityMob;
import net.minecraft.entity.monster.IMob;
import net.minecraft.entity.passive.EntityHorse;
import net.minecraft.entity.passive.EntityPig;
import net.minecraft.entity.projectile.EntityFishHook;
import net.minecraft.event.ClickEvent;
import net.minecraft.init.Blocks;
import net.minecraft.init.Items;
import net.minecraft.inventory.Container;
import net.minecraft.inventory.ContainerPlayer;
import net.minecraft.inventory.IInventory;
import net.minecraft.inventory.InventoryEnderChest;
import net.minecraft.item.*;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.nbt.NBTTagList;
import net.minecraft.network.play.server.S12PacketEntityVelocity;
import net.minecraft.potion.Potion;
import net.minecraft.scoreboard.*;
import net.minecraft.server.MinecraftServer;
import net.minecraft.stats.AchievementList;
import net.minecraft.stats.StatBase;
import net.minecraft.stats.StatList;
import net.minecraft.tileentity.TileEntitySign;
import net.minecraft.util.*;
import net.minecraft.world.*;

import java.util.Collection;
import java.util.List;
import java.util.UUID;

import static net.minecraft.client.MinecraftInstance.mc;

@SuppressWarnings("incomplete-switch")
public abstract class EntityPlayer extends EntityLivingBase {
    public InventoryPlayer inventory = new InventoryPlayer(this);
    public final Container inventoryContainer;
    public final PlayerCapabilities capabilities = new PlayerCapabilities();
    protected final float speedInAir = 0.02F;
    private final GameProfile gameProfile;
    public Container openContainer;
    public float prevCameraYaw;
    public float cameraYaw;
    public int xpCooldown;
    public double prevChasingPosX;
    public double prevChasingPosY;
    public double prevChasingPosZ;
    public double chasingPosX;
    public double chasingPosY;
    public double chasingPosZ;
    public BlockPos playerLocation;
    public float renderOffsetX;
    public float renderOffsetY;
    public float renderOffsetZ;
    public int experienceLevel;
    public int experienceTotal;
    public float experience;
    public EntityFishHook fishEntity;
    protected FoodStats foodStats = new FoodStats();
    protected int flyToggleTimer;
    public boolean sleeping;
    protected float speedOnGround = 0.1F;
    private InventoryEnderChest theInventoryEnderChest = new InventoryEnderChest();
    private int sleepTimer;
    private BlockPos spawnChunk;
    private boolean spawnForced;
    private BlockPos startMinecartRidingCoordinate;
    private int xpSeed;
    private ItemStack itemInUse;
    private int itemInUseCount;
    private int lastXPSound;
    private boolean hasReducedDebug = false;

    // Start of BloomClient

    public int ticksSinceLastSwing = 0;

    // End of BloomClient

    public EntityPlayer(World worldIn, GameProfile gameProfileIn) {
        super(worldIn);
        this.entityUniqueID = getUUID(gameProfileIn);
        this.gameProfile = gameProfileIn;
        this.inventoryContainer = new ContainerPlayer(this.inventory, !worldIn.isRemote, this);
        this.openContainer = this.inventoryContainer;
        BlockPos blockpos = worldIn.getSpawnPoint();
        this.setLocationAndAngles((double) blockpos.getX() + 0.5D, blockpos.getY() + 1, (double) blockpos.getZ() + 0.5D, 0.0F, 0.0F);
        this.unused180 = 180.0F;
        this.fireResistance = 20;
    }

    public static BlockPos getBedSpawnLocation(World worldIn, BlockPos bedLocation, boolean forceSpawn) {
        Block block = worldIn.getBlockState(bedLocation).getBlock();

        if (block != Blocks.bed) {
            if (!forceSpawn) {
                return null;
            } else {
                boolean flag = block.canSpawnInBlock();
                boolean flag1 = worldIn.getBlockState(bedLocation.up()).getBlock().canSpawnInBlock();
                return flag && flag1 ? bedLocation : null;
            }
        } else {
            return BlockBed.getSafeExitLocation(worldIn, bedLocation, 0);
        }
    }

    public static UUID getUUID(GameProfile profile) {
        UUID uuid = profile.getId();

        if (uuid == null) {
            uuid = getOfflineUUID(profile.getName());
        }

        return uuid;
    }

    public static UUID getOfflineUUID(String username) {
        return UUID.nameUUIDFromBytes(("OfflinePlayer:" + username).getBytes(Charsets.UTF_8));
    }

    protected void applyEntityAttributes() {
        super.applyEntityAttributes();
        this.getAttributeMap().registerAttribute(SharedMonsterAttributes.attackDamage).setBaseValue(1.0D);
        this.getEntityAttribute(SharedMonsterAttributes.movementSpeed).setBaseValue(0.10000000149011612D);
    }

    protected void entityInit() {
        super.entityInit();
        this.dataWatcher.addObject(16, (byte) 0);
        this.dataWatcher.addObject(17, 0.0F);
        this.dataWatcher.addObject(18, 0);
        this.dataWatcher.addObject(10, (byte) 0);
    }

    public ItemStack getItemInUse() {
        return this.itemInUse;
    }

    public int getItemInUseCount() {
        ItemStack stack = mc.thePlayer.getHeldItem();

        final ModuleKillAura killAura = ModuleKillAura.INSTANCE;

        if (stack != null && stack.getItem() instanceof ItemSword && killAura.getAutoBlock().get() && killAura.getRenderBlocking()) {
            return Integer.MAX_VALUE;
        }

        return this.itemInUseCount;
    }

    public boolean isUsingItem() {
        return this.itemInUse != null;
    }

    public int getItemInUseDuration() {
        return this.isUsingItem() ? this.itemInUse.getMaxItemUseDuration() - this.itemInUseCount : 0;
    }

    public void stopUsingItem() {
        if (this.itemInUse != null) {
            this.itemInUse.onPlayerStoppedUsing(this.worldObj, this, this.itemInUseCount);
        }

        this.clearItemInUse();
    }

    public void clearItemInUse() {
        this.itemInUse = null;
        this.itemInUseCount = 0;

        if (!this.worldObj.isRemote) {
            this.setEating(false);
        }
    }

    public boolean isBlocking() {
        return this.isUsingItem() && this.itemInUse.getItem().getItemUseAction(this.itemInUse) == EnumAction.BLOCK;
    }

    public void onUpdate() {
        this.noClip = this.isSpectator();

        if (this.isSpectator()) {
            this.onGround = false;
        }

        if (this.itemInUse != null) {
            ItemStack itemstack = this.inventory.getCurrentItemStack();

            if (itemstack == this.itemInUse) {
                if (this.itemInUseCount <= 25 && this.itemInUseCount % 4 == 0) {
                    this.updateItemUse(itemstack, 5);
                }

                if (--this.itemInUseCount == 0 && !this.worldObj.isRemote) {
                    this.onItemUseFinish();
                }
            } else {
                this.clearItemInUse();
            }
        }

        if (this.xpCooldown > 0) {
            --this.xpCooldown;
        }

        if (this.isPlayerSleeping()) {
            ++this.sleepTimer;

            if (this.sleepTimer > 100) {
                this.sleepTimer = 100;
            }

            if (!this.worldObj.isRemote) {
                if (!this.isInBed()) {
                    this.wakeUpPlayer(true, true, false);
                } else if (this.worldObj.isDaytime()) {
                    this.wakeUpPlayer(false, true, true);
                }
            }
        } else if (this.sleepTimer > 0) {
            ++this.sleepTimer;

            if (this.sleepTimer >= 110) {
                this.sleepTimer = 0;
            }
        }

        super.onUpdate();

        if (!this.worldObj.isRemote && this.openContainer != null && !this.openContainer.canInteractWith(this)) {
            this.closeScreen();
            this.openContainer = this.inventoryContainer;
        }

        if (this.isBurning() && this.capabilities.disableDamage) {
            this.extinguish();
        }

        this.prevChasingPosX = this.chasingPosX;
        this.prevChasingPosY = this.chasingPosY;
        this.prevChasingPosZ = this.chasingPosZ;
        double d5 = this.posX - this.chasingPosX;
        double d0 = this.posY - this.chasingPosY;
        double d1 = this.posZ - this.chasingPosZ;
        double d2 = 10.0D;

        if (d5 > d2) {
            this.prevChasingPosX = this.chasingPosX = this.posX;
        }

        if (d1 > d2) {
            this.prevChasingPosZ = this.chasingPosZ = this.posZ;
        }

        if (d0 > d2) {
            this.prevChasingPosY = this.chasingPosY = this.posY;
        }

        if (d5 < -d2) {
            this.prevChasingPosX = this.chasingPosX = this.posX;
        }

        if (d1 < -d2) {
            this.prevChasingPosZ = this.chasingPosZ = this.posZ;
        }

        if (d0 < -d2) {
            this.prevChasingPosY = this.chasingPosY = this.posY;
        }

        this.chasingPosX += d5 * 0.25D;
        this.chasingPosZ += d1 * 0.25D;
        this.chasingPosY += d0 * 0.25D;

        if (this.ridingEntity == null) {
            this.startMinecartRidingCoordinate = null;
        }

        if (!this.worldObj.isRemote) {
            this.foodStats.onUpdate(this);
            this.triggerAchievement(StatList.minutesPlayedStat);

            if (this.isEntityAlive()) {
                this.triggerAchievement(StatList.timeSinceDeathStat);
            }
        }

        int i = 29999999;
        double d3 = MathHelper.clamp_double(this.posX, -2.9999999E7D, 2.9999999E7D);
        double d4 = MathHelper.clamp_double(this.posZ, -2.9999999E7D, 2.9999999E7D);

        if (d3 != this.posX || d4 != this.posZ) {
            this.setPosition(d3, this.posY, d4);
        }
    }

    public int getMaxInPortalTime() {
        return this.capabilities.disableDamage ? 0 : 80;
    }

    protected String getSwimSound() {
        return "game.player.swim";
    }

    protected String getSplashSound() {
        return "game.player.swim.splash";
    }

    public int getPortalCooldown() {
        return 10;
    }

    public void playSound(String name, float volume, float pitch) {
        this.worldObj.playSoundToNearExcept(this, name, volume, pitch);
    }

    protected void updateItemUse(ItemStack itemStackIn, int ticks) {
        if (itemStackIn.getItemUseAction() == EnumAction.DRINK) {
            this.playSound("random.drink", 0.5F, this.worldObj.rand.nextFloat() * 0.1F + 0.9F);
        }

        if (itemStackIn.getItemUseAction() == EnumAction.EAT) {
            for (int i = 0; i < ticks; ++i) {
                Vec3 vec3 = new Vec3(((double) this.rand.nextFloat() - 0.5D) * 0.1D, Math.random() * 0.1D + 0.1D, 0.0D);
                vec3 = vec3.rotatePitch(-this.rotationPitch * (float) Math.PI / 180.0F);
                vec3 = vec3.rotateYaw(-this.rotationYaw * (float) Math.PI / 180.0F);
                double d0 = (double) (-this.rand.nextFloat()) * 0.6D - 0.3D;
                Vec3 vec31 = new Vec3(((double) this.rand.nextFloat() - 0.5D) * 0.3D, d0, 0.6D);
                vec31 = vec31.rotatePitch(-this.rotationPitch * (float) Math.PI / 180.0F);
                vec31 = vec31.rotateYaw(-this.rotationYaw * (float) Math.PI / 180.0F);
                vec31 = vec31.addVector(this.posX, this.posY + (double) this.getEyeHeight(), this.posZ);

                if (itemStackIn.getHasSubtypes()) {
                    this.worldObj.spawnParticle(EnumParticleTypes.ITEM_CRACK, vec31.xCoord, vec31.yCoord, vec31.zCoord, vec3.xCoord, vec3.yCoord + 0.05D, vec3.zCoord, Item.getIdFromItem(itemStackIn.getItem()), itemStackIn.getMetadata());
                } else {
                    this.worldObj.spawnParticle(EnumParticleTypes.ITEM_CRACK, vec31.xCoord, vec31.yCoord, vec31.zCoord, vec3.xCoord, vec3.yCoord + 0.05D, vec3.zCoord, Item.getIdFromItem(itemStackIn.getItem()));
                }
            }

            this.playSound("random.eat", 0.5F + 0.5F * (float) this.rand.nextInt(2), (this.rand.nextFloat() - this.rand.nextFloat()) * 0.2F + 1.0F);
        }
    }

    protected void onItemUseFinish() {
        if (this.itemInUse != null) {
            this.updateItemUse(this.itemInUse, 16);
            int i = this.itemInUse.stackSize;
            ItemStack itemstack = this.itemInUse.onItemUseFinish(this.worldObj, this);

            if (itemstack != this.itemInUse || itemstack != null && itemstack.stackSize != i) {
                this.inventory.mainInventory[this.inventory.currentItem] = itemstack;

                if (itemstack.stackSize == 0) {
                    this.inventory.mainInventory[this.inventory.currentItem] = null;
                }
            }

            this.clearItemInUse();
        }
    }

    public void handleStatusUpdate(byte id) {
        if (id == 9) {
            this.onItemUseFinish();
        } else if (id == 23) {
            this.hasReducedDebug = false;
        } else if (id == 22) {
            this.hasReducedDebug = true;
        } else {
            super.handleStatusUpdate(id);
        }
    }

    protected boolean isMovementBlocked() {
        return this.getHealth() <= 0.0F || this.isPlayerSleeping();
    }

    protected void closeScreen() {
        this.openContainer = this.inventoryContainer;
    }

    public void updateRidden() {
        if (!this.worldObj.isRemote && this.isSneaking()) {
            this.mountEntity(null);
            this.setSneaking(false);
        } else {
            double d0 = this.posX;
            double d1 = this.posY;
            double d2 = this.posZ;
            float f = this.rotationYaw;
            float f1 = this.rotationPitch;
            super.updateRidden();
            this.prevCameraYaw = this.cameraYaw;
            this.cameraYaw = 0.0F;
            this.addMountedMovementStat(this.posX - d0, this.posY - d1, this.posZ - d2);

            if (this.ridingEntity instanceof EntityPig) {
                this.rotationPitch = f1;
                this.rotationYaw = f;
                this.renderYawOffset = ((EntityPig) this.ridingEntity).renderYawOffset;
            }
        }
    }

    public void preparePlayerToSpawn() {
        this.setSize(0.6F, 1.8F);
        super.preparePlayerToSpawn();
        this.setHealth(this.getMaxHealth());
        this.deathTime = 0;
    }

    protected void updateEntityActionState() {
        super.updateEntityActionState();
        this.updateArmSwingProgress();
        this.rotationYawHead = this.rotationYaw;
    }

    public void onLivingUpdate() {
        if (this.flyToggleTimer > 0) {
            --this.flyToggleTimer;
        }

        if (this.worldObj.getDifficulty() == EnumDifficulty.PEACEFUL && this.worldObj.getGameRules().getBoolean("naturalRegeneration")) {
            if (this.getHealth() < this.getMaxHealth() && this.ticksExisted % 20 == 0) {
                this.heal(1.0F);
            }

            if (this.foodStats.needFood() && this.ticksExisted % 10 == 0) {
                this.foodStats.setFoodLevel(this.foodStats.getFoodLevel() + 1);
            }
        }

        this.inventory.decrementAnimations();
        this.prevCameraYaw = this.cameraYaw;
        super.onLivingUpdate();
        IAttributeInstance iattributeinstance = this.getEntityAttribute(SharedMonsterAttributes.movementSpeed);

        if (!this.worldObj.isRemote) {
            iattributeinstance.setBaseValue(this.capabilities.getWalkSpeed());
        }

        this.jumpMovementFactor = this.speedInAir;

        if (this.isSprinting()) {
            this.jumpMovementFactor = (float) ((double) this.jumpMovementFactor + (double) this.speedInAir * 0.3D);
        }

        this.setAIMoveSpeed((float) iattributeinstance.getAttributeValue());
        float f = MathHelper.sqrt_double(this.motionX * this.motionX + this.motionZ * this.motionZ);
        float f1 = (float) (Math.atan(-this.motionY * 0.20000000298023224D) * 15.0D);

        if (f > 0.1F) {
            f = 0.1F;
        }

        if (!this.onGround || this.getHealth() <= 0.0F) {
            f = 0.0F;
        }

        if (this.onGround || this.getHealth() <= 0.0F) {
            f1 = 0.0F;
        }

        this.cameraYaw += (f - this.cameraYaw) * 0.4F;
        this.cameraPitch += (f1 - this.cameraPitch) * 0.8F;

        if (this.getHealth() > 0.0F && !this.isSpectator()) {
            AxisAlignedBB axisalignedbb;

            if (this.ridingEntity != null && !this.ridingEntity.isDead) {
                axisalignedbb = this.getEntityBoundingBox().union(this.ridingEntity.getEntityBoundingBox()).expand(1.0D, 0.0D, 1.0D);
            } else {
                axisalignedbb = this.getEntityBoundingBox().expand(1.0D, 0.5D, 1.0D);
            }

            List<Entity> list = this.worldObj.getEntitiesWithinAABBExcludingEntity(this, axisalignedbb);

            for (Entity entity : list) {
                if (!entity.isDead) {
                    this.collideWithPlayer(entity);
                }
            }
        }
    }

    private void collideWithPlayer(Entity entityIn) {
        entityIn.onCollideWithPlayer(this);
    }

    public int getScore() {
        return this.dataWatcher.getWatchableObjectInt(18);
    }

    public void setScore(int scoreIn) {
        this.dataWatcher.updateObject(18, scoreIn);
    }

    public void addScore(int scoreIn) {
        int i = this.getScore();
        this.dataWatcher.updateObject(18, i + scoreIn);
    }

    public void onDeath(DamageSource cause) {
        super.onDeath(cause);
        this.setSize(0.2F, 0.2F);
        this.setPosition(this.posX, this.posY, this.posZ);
        this.motionY = 0.10000000149011612D;

        if (this.getName().equals("Notch")) {
            this.dropItem(new ItemStack(Items.apple, 1), true, false);
        }

        if (!this.worldObj.getGameRules().getBoolean("keepInventory")) {
            this.inventory.dropAllItems();
        }

        this.motionX = -MathHelper.cos((this.attackedAtYaw + this.rotationYaw) * (float) Math.PI / 180.0F) * 0.1F;
        this.motionZ = -MathHelper.sin((this.attackedAtYaw + this.rotationYaw) * (float) Math.PI / 180.0F) * 0.1F;

        this.triggerAchievement(StatList.deathsStat);
        this.takeStat(StatList.timeSinceDeathStat);
    }

    protected String getHurtSound() {
        return "game.player.hurt";
    }

    protected String getDeathSound() {
        return "game.player.die";
    }

    public void addToPlayerScore(Entity entityIn, int amount) {
        this.addScore(amount);
        Collection<ScoreObjective> collection = this.getWorldScoreboard().getObjectivesFromCriteria(IScoreObjectiveCriteria.totalKillCount);

        if (entityIn instanceof EntityPlayer) {
            this.triggerAchievement(StatList.playerKillsStat);
            collection.addAll(this.getWorldScoreboard().getObjectivesFromCriteria(IScoreObjectiveCriteria.playerKillCount));
            collection.addAll(this.giveTeamKillScores(entityIn));
        } else {
            this.triggerAchievement(StatList.mobKillsStat);
        }

        for (ScoreObjective scoreobjective : collection) {
            Score score = this.getWorldScoreboard().getValueFromObjective(this.getName(), scoreobjective);
            score.incrementScore();
        }
    }

    private Collection<ScoreObjective> giveTeamKillScores(Entity entity) {
        ScorePlayerTeam scoreplayerteam = this.getWorldScoreboard().getPlayersTeam(this.getName());

        if (scoreplayerteam != null) {
            int i = scoreplayerteam.getChatFormat().getColorIndex();

            if (i >= 0 && i < IScoreObjectiveCriteria.KILLED_BY_TEAM.length) {
                for (ScoreObjective scoreobjective : this.getWorldScoreboard().getObjectivesFromCriteria(IScoreObjectiveCriteria.KILLED_BY_TEAM[i])) {
                    Score score = this.getWorldScoreboard().getValueFromObjective(entity.getName(), scoreobjective);
                    score.incrementScore();
                }
            }
        }

        ScorePlayerTeam scoreplayerteam1 = this.getWorldScoreboard().getPlayersTeam(entity.getName());

        if (scoreplayerteam1 != null) {
            int j = scoreplayerteam1.getChatFormat().getColorIndex();

            if (j >= 0 && j < IScoreObjectiveCriteria.TEAM_KILL.length) {
                return this.getWorldScoreboard().getObjectivesFromCriteria(IScoreObjectiveCriteria.TEAM_KILL[j]);
            }
        }

        return Lists.newArrayList();
    }

    public EntityItem dropOneItem(boolean dropAll) {
        return this.dropItem(this.inventory.decrStackSize(this.inventory.currentItem, dropAll && this.inventory.getCurrentItemStack() != null ? this.inventory.getCurrentItemStack().stackSize : 1), false, true);
    }

    public EntityItem dropPlayerItemWithRandomChoice(ItemStack itemStackIn, boolean unused) {
        return this.dropItem(itemStackIn, false, false);
    }

    public EntityItem dropItem(ItemStack droppedItem, boolean dropAround, boolean traceItem) {
        if (droppedItem == null) {
            return null;
        } else if (droppedItem.stackSize == 0) {
            return null;
        } else {
            DropItemSwing.swingOnDrop();
            double d0 = this.posY - 0.30000001192092896D + (double) this.getEyeHeight();
            EntityItem entityitem = new EntityItem(this.worldObj, this.posX, d0, this.posZ, droppedItem);
            entityitem.setPickupDelay(40);

            if (traceItem) {
                entityitem.setThrower(this.getName());
            }

            if (dropAround) {
                float f = this.rand.nextFloat() * 0.5F;
                float f1 = this.rand.nextFloat() * (float) Math.PI * 2.0F;
                entityitem.motionX = -MathHelper.sin(f1) * f;
                entityitem.motionZ = MathHelper.cos(f1) * f;
                entityitem.motionY = 0.20000000298023224D;
            } else {
                float f2 = 0.3F;
                entityitem.motionX = -MathHelper.sin(this.rotationYaw / 180.0F * (float) Math.PI) * MathHelper.cos(this.rotationPitch / 180.0F * (float) Math.PI) * f2;
                entityitem.motionZ = MathHelper.cos(this.rotationYaw / 180.0F * (float) Math.PI) * MathHelper.cos(this.rotationPitch / 180.0F * (float) Math.PI) * f2;
                entityitem.motionY = -MathHelper.sin(this.rotationPitch / 180.0F * (float) Math.PI) * f2 + 0.1F;
                float f3 = this.rand.nextFloat() * (float) Math.PI * 2.0F;
                f2 = 0.02F * this.rand.nextFloat();
                entityitem.motionX += Math.cos(f3) * (double) f2;
                entityitem.motionY += (this.rand.nextFloat() - this.rand.nextFloat()) * 0.1F;
                entityitem.motionZ += Math.sin(f3) * (double) f2;
            }

            this.joinEntityItemWithWorld(entityitem);

            if (traceItem) {
                this.triggerAchievement(StatList.dropStat);
            }

            return entityitem;
        }
    }

    protected void joinEntityItemWithWorld(EntityItem itemIn) {
        this.worldObj.spawnEntityInWorld(itemIn);
    }

    public float getToolDigEfficiency(Block block) {
        float f = this.inventory.getStrVsBlock(block);

        if (f > 1.0F) {
            int i = EnchantmentHelper.getEfficiencyModifier(this);
            ItemStack itemstack = this.inventory.getCurrentItemStack();

            if (i > 0 && itemstack != null) {
                f += (float) (i * i + 1);
            }
        }

        if (this.isPotionActive(Potion.digSpeed)) {
            f *= 1.0F + (float) (this.getActivePotionEffect(Potion.digSpeed).getAmplifier() + 1) * 0.2F;
        }

        if (this.isPotionActive(Potion.digSlowdown)) {
            float f1 = switch (this.getActivePotionEffect(Potion.digSlowdown).getAmplifier()) {
                case 0 -> 0.3F;
                case 1 -> 0.09F;
                case 2 -> 0.0027F;
                default -> 8.1E-4F;
            };

            f *= f1;
        }

        if (this.isInsideOfMaterial(Material.water) && !EnchantmentHelper.getAquaAffinityModifier(this)) {
            f /= 5.0F;
        }

        if (!this.onGround) {
            f /= 5.0F;
        }

        return f;
    }

    public boolean canHarvestBlock(Block blockToHarvest) {
        return this.inventory.canHeldItemHarvest(blockToHarvest);
    }

    public void readEntityFromNBT(NBTTagCompound tagCompund) {
        super.readEntityFromNBT(tagCompund);
        this.entityUniqueID = getUUID(this.gameProfile);
        NBTTagList nbttaglist = tagCompund.getTagList("Inventory", 10);
        this.inventory.readFromNBT(nbttaglist);
        this.inventory.currentItem = tagCompund.getInteger("SelectedItemSlot");
        this.sleeping = tagCompund.getBoolean("Sleeping");
        this.sleepTimer = tagCompund.getShort("SleepTimer");
        this.experience = tagCompund.getFloat("XpP");
        this.experienceLevel = tagCompund.getInteger("XpLevel");
        this.experienceTotal = tagCompund.getInteger("XpTotal");
        this.xpSeed = tagCompund.getInteger("XpSeed");

        if (this.xpSeed == 0) {
            this.xpSeed = this.rand.nextInt();
        }

        this.setScore(tagCompund.getInteger("Score"));

        if (this.sleeping) {
            this.playerLocation = new BlockPos(this);
            this.wakeUpPlayer(true, true, false);
        }

        if (tagCompund.hasKey("SpawnX", 99) && tagCompund.hasKey("SpawnY", 99) && tagCompund.hasKey("SpawnZ", 99)) {
            this.spawnChunk = new BlockPos(tagCompund.getInteger("SpawnX"), tagCompund.getInteger("SpawnY"), tagCompund.getInteger("SpawnZ"));
            this.spawnForced = tagCompund.getBoolean("SpawnForced");
        }

        this.foodStats.readNBT(tagCompund);
        this.capabilities.readCapabilitiesFromNBT(tagCompund);

        if (tagCompund.hasKey("EnderItems", 9)) {
            NBTTagList nbttaglist1 = tagCompund.getTagList("EnderItems", 10);
            this.theInventoryEnderChest.loadInventoryFromNBT(nbttaglist1);
        }
    }

    public void writeEntityToNBT(NBTTagCompound tagCompound) {
        super.writeEntityToNBT(tagCompound);
        tagCompound.setTag("Inventory", this.inventory.writeToNBT(new NBTTagList()));
        tagCompound.setInteger("SelectedItemSlot", this.inventory.currentItem);
        tagCompound.setBoolean("Sleeping", this.sleeping);
        tagCompound.setShort("SleepTimer", (short) this.sleepTimer);
        tagCompound.setFloat("XpP", this.experience);
        tagCompound.setInteger("XpLevel", this.experienceLevel);
        tagCompound.setInteger("XpTotal", this.experienceTotal);
        tagCompound.setInteger("XpSeed", this.xpSeed);
        tagCompound.setInteger("Score", this.getScore());

        if (this.spawnChunk != null) {
            tagCompound.setInteger("SpawnX", this.spawnChunk.getX());
            tagCompound.setInteger("SpawnY", this.spawnChunk.getY());
            tagCompound.setInteger("SpawnZ", this.spawnChunk.getZ());
            tagCompound.setBoolean("SpawnForced", this.spawnForced);
        }

        this.foodStats.writeNBT(tagCompound);
        this.capabilities.writeCapabilitiesToNBT(tagCompound);
        tagCompound.setTag("EnderItems", this.theInventoryEnderChest.saveInventoryToNBT());
        ItemStack itemstack = this.inventory.getCurrentItemStack();

        if (itemstack != null && itemstack.getItem() != null) {
            tagCompound.setTag("SelectedItem", itemstack.writeToNBT(new NBTTagCompound()));
        }
    }

    public boolean attackEntityFrom(DamageSource source, float amount) {
        if (this.isEntityInvulnerable(source)) {
            return false;
        } else if (this.capabilities.disableDamage && !source.canHarmInCreative()) {
            return false;
        } else {
            this.entityAge = 0;

            if (this.getHealth() <= 0.0F) {
                return false;
            } else {
                if (this.isPlayerSleeping() && !this.worldObj.isRemote) {
                    this.wakeUpPlayer(true, true, false);
                }

                if (source.isDifficultyScaled()) {
                    if (this.worldObj.getDifficulty() == EnumDifficulty.PEACEFUL) {
                        amount = 0.0F;
                    }

                    if (this.worldObj.getDifficulty() == EnumDifficulty.EASY) {
                        amount = amount / 2.0F + 1.0F;
                    }

                    if (this.worldObj.getDifficulty() == EnumDifficulty.HARD) {
                        amount = amount * 3.0F / 2.0F;
                    }
                }

                if (amount == 0.0F) {
                    return false;
                } else {
                    Entity entity = source.getEntity();

                    return super.attackEntityFrom(source, amount);
                }
            }
        }
    }

    public boolean canAttackPlayer(EntityPlayer other) {
        Team team = this.getTeam();
        Team team1 = other.getTeam();
        return team == null || (!team.isSameTeam(team1) || team.getAllowFriendlyFire());
    }

    protected void damageArmor(float damage) {
        this.inventory.damageArmor(damage);
    }

    public int getTotalArmorValue() {
        return this.inventory.getTotalArmorValue();
    }

    public float getArmorVisibility() {
        int i = 0;

        for (ItemStack itemstack : this.inventory.armorInventory) {
            if (itemstack != null) {
                ++i;
            }
        }

        return (float) i / (float) this.inventory.armorInventory.length;
    }

    protected void damageEntity(DamageSource damageSrc, float damageAmount) {
        if (!this.isEntityInvulnerable(damageSrc)) {
            if (!damageSrc.isUnblockable() && this.isBlocking() && damageAmount > 0.0F) {
                damageAmount = (1.0F + damageAmount) * 0.5F;
            }

            damageAmount = this.applyArmorCalculations(damageSrc, damageAmount);
            damageAmount = this.applyPotionDamageCalculations(damageSrc, damageAmount);
            float f = damageAmount;
            damageAmount = Math.max(damageAmount - this.getAbsorptionAmount(), 0.0F);
            this.setAbsorptionAmount(this.getAbsorptionAmount() - (f - damageAmount));

            if (damageAmount != 0.0F) {
                this.addExhaustion(damageSrc.getHungerDamage());
                float f1 = this.getHealth();
                this.setHealth(this.getHealth() - damageAmount);
                this.getCombatTracker().trackDamage(damageSrc, f1, damageAmount);

                if (damageAmount < 3.4028235E37F) {
                    this.addStat(StatList.damageTakenStat, Math.round(damageAmount * 10.0F));
                }
            }
        }
    }

    public void openEditSign(TileEntitySign signTile) {
    }

    public void openEditCommandBlock(CommandBlockLogic cmdBlockLogic) {
    }

    public void displayVillagerTradeGui(IMerchant villager) {
    }

    public void displayGUIChest(IInventory chestInventory) {
    }

    public void displayGUIHorse(EntityHorse horse, IInventory horseInventory) {
    }

    public void displayGui(IInteractionObject guiOwner) {
    }

    public void displayGUIBook(ItemStack bookStack) {
    }

    public boolean interactWith(Entity targetEntity) {
        if (this.isSpectator()) {
            if (targetEntity instanceof IInventory) {
                this.displayGUIChest((IInventory) targetEntity);
            }

            return false;
        } else {
            ItemStack itemstack = this.getCurrentEquippedItem();
            ItemStack itemstack1 = itemstack != null ? itemstack.copy() : null;

            if (!targetEntity.interactFirst(this)) {
                if (itemstack != null && targetEntity instanceof EntityLivingBase) {
                    if (this.capabilities.isCreativeMode) {
                        itemstack = itemstack1;
                    }

                    if (itemstack.interactWithEntity(this, (EntityLivingBase) targetEntity)) {
                        if (itemstack.stackSize <= 0 && !this.capabilities.isCreativeMode) {
                            this.destroyCurrentEquippedItem();
                        }

                        return true;
                    }
                }

                return false;
            } else {
                if (itemstack != null && itemstack == this.getCurrentEquippedItem()) {
                    if (itemstack.stackSize <= 0 && !this.capabilities.isCreativeMode) {
                        this.destroyCurrentEquippedItem();
                    } else if (itemstack.stackSize < itemstack1.stackSize && this.capabilities.isCreativeMode) {
                        itemstack.stackSize = itemstack1.stackSize;
                    }
                }

                return true;
            }
        }
    }

    public ItemStack getCurrentEquippedItem() {
        return this.inventory.getCurrentItemStack();
    }

    public void destroyCurrentEquippedItem() {
        this.inventory.setInventorySlotContents(this.inventory.currentItem, null);
    }

    public double getYOffset() {
        return -0.35D;
    }

    public void attackTargetEntityWithCurrentItem(Entity targetEntity) {
        if (targetEntity.canAttackWithItem() && !targetEntity.hitByEntity(this)) {
            float f = (float)this.getEntityAttribute(SharedMonsterAttributes.attackDamage).getAttributeValue();
            int i = 0;
            float f2 = 0.0F;
            if (targetEntity instanceof EntityLivingBase) {
                f2 = EnchantmentHelper.getModifierForCreature(this.getHeldItem(), ((EntityLivingBase)targetEntity).getCreatureAttribute());
            } else {
                f2 = EnchantmentHelper.getModifierForCreature(this.getHeldItem(), EnumCreatureAttribute.UNDEFINED);
            }

            i += EnchantmentHelper.getKnockbackModifier(this);
            if (this.isSprinting()) {
                ++i;
            }

            if (f > 0.0F || f2 > 0.0F) {
                boolean flag = this.fallDistance > 0.0F && !this.onGround && !this.isOnLadder() && !this.isInWater() && !this.isPotionActive(Potion.blindness) && this.ridingEntity == null && targetEntity instanceof EntityLivingBase;
                if (flag && f > 0.0F) {
                    f *= 1.5F;
                }

                f += f2;
                boolean flag2 = false;
                int j = EnchantmentHelper.getFireAspectModifier(this);
                if (targetEntity instanceof EntityLivingBase && j > 0 && !targetEntity.isBurning()) {
                    flag2 = true;
                    targetEntity.setFire(1);
                }

                double d0 = targetEntity.motionX;
                double d2 = targetEntity.motionY;
                double d3 = targetEntity.motionZ;
                boolean flag3 = targetEntity.attackEntityFrom(DamageSource.causePlayerDamage(this), f);
                if (flag3) {
                    KnockbackEvent eventAttackSlowdown = new KnockbackEvent(0.6, false, 1, 0, false, false, true);
                    BloomClient.eventManager.call(eventAttackSlowdown);
                    float yaw = this.playerYaw;
                    int power;
                    if (i > 0) {
                        if (!eventAttackSlowdown.isCancelled() && eventAttackSlowdown.getFlag()) {
                            for(power = 0; power < eventAttackSlowdown.getPower(); ++power) {
                                targetEntity.addVelocity(-MathHelper.sin(yaw * 3.1415927F / 180.0F) * (float)i * 0.5F, eventAttackSlowdown.getReduceY() ? 0.0D : 0.1D, MathHelper.cos(yaw * 3.1415927F / 180.0F) * (float)i * 0.5F);
                            }

                            this.motionX *= eventAttackSlowdown.getMotion();
                            this.motionZ *= eventAttackSlowdown.getMotion();

                            //KeepSprint
                            if(!eventAttackSlowdown.getSprint()){
                                this.setSprinting(false);
                            }
                        }
                    } else if (!eventAttackSlowdown.isCancelled() && eventAttackSlowdown.getFull() && Minecraft.getMinecraft().thePlayer.hurtTime > 0 && eventAttackSlowdown.getFlag()) {
                        for(power = 0; power < eventAttackSlowdown.getPostPower(); ++power) {
                            targetEntity.addVelocity(-MathHelper.sin(yaw * 3.1415927F / 180.0F) * (float)i * 0.5F, eventAttackSlowdown.getReduceY() ? 0.0D : 0.1D, MathHelper.cos(yaw * 3.1415927F / 180.0F) * (float)i * 0.5F);
                        }

                        this.motionX *= eventAttackSlowdown.getMotion();
                        this.motionZ *= eventAttackSlowdown.getMotion();

                        //KeepSprint
                        if(!eventAttackSlowdown.getSprint()){
                            this.setSprinting(false);
                        }
                    }

                    if (targetEntity instanceof EntityPlayerMP && targetEntity.velocityChanged) {
                        ((EntityPlayerMP)targetEntity).playerNetServerHandler.sendPacket(new S12PacketEntityVelocity(targetEntity));
                        targetEntity.velocityChanged = false;
                        targetEntity.motionX = d0;
                        targetEntity.motionY = d2;
                        targetEntity.motionZ = d3;
                    }

                    if (flag) {
                        this.onCriticalHit(targetEntity);
                    }

                    if (f2 > 0.0F) {
                        this.onEnchantmentCritical(targetEntity);
                    }

                    if (f >= 18.0F) {
                        this.triggerAchievement(AchievementList.overkill);
                    }

                    this.setLastAttacker(targetEntity);
                    if (targetEntity instanceof EntityLivingBase) {
                        EnchantmentHelper.applyThornEnchantments((EntityLivingBase)targetEntity, this);
                    }

                    EnchantmentHelper.applyArthropodEnchantments(this, targetEntity);
                    ItemStack itemstack = this.getCurrentEquippedItem();
                    Entity entity = targetEntity;
                    if (targetEntity instanceof EntityDragonPart) {
                        IEntityMultiPart ientitymultipart = ((EntityDragonPart)targetEntity).entityDragonObj;
                        if (ientitymultipart instanceof EntityLivingBase) {
                            entity = (EntityLivingBase)ientitymultipart;
                        }
                    }

                    if (itemstack != null && entity instanceof EntityLivingBase) {
                        itemstack.hitEntity((EntityLivingBase)entity, this);
                        if (itemstack.stackSize <= 0) {
                            this.destroyCurrentEquippedItem();
                        }
                    }

                    if (targetEntity instanceof EntityLivingBase) {
                        this.addStat(StatList.damageDealtStat, Math.round(f * 10.0F));
                        if (j > 0) {
                            targetEntity.setFire(j * 4);
                        }
                    }

                    this.addExhaustion(0.3F);
                } else if (flag2) {
                    targetEntity.extinguish();
                }
            }
        }
    }

    public void onCriticalHit(Entity entityHit) {
    }

    public void onEnchantmentCritical(Entity entityHit) {
    }

    public void respawnPlayer() {
    }

    public void setDead() {
        super.setDead();
        this.inventoryContainer.onContainerClosed(this);

        if (this.openContainer != null) {
            this.openContainer.onContainerClosed(this);
        }
    }

    public boolean isEntityInsideOpaqueBlock() {
        return !this.sleeping && super.isEntityInsideOpaqueBlock();
    }

    public boolean isUser() {
        return false;
    }

    public GameProfile getGameProfile() {
        return this.gameProfile;
    }

    public EntityPlayer.EnumStatus trySleep(BlockPos bedLocation) {
        if (!this.worldObj.isRemote) {
            if (this.isPlayerSleeping() || !this.isEntityAlive()) {
                return EntityPlayer.EnumStatus.OTHER_PROBLEM;
            }

            if (!this.worldObj.provider.isSurfaceWorld()) {
                return EntityPlayer.EnumStatus.NOT_POSSIBLE_HERE;
            }

            if (this.worldObj.isDaytime()) {
                return EntityPlayer.EnumStatus.NOT_POSSIBLE_NOW;
            }

            if (Math.abs(this.posX - (double) bedLocation.getX()) > 3.0D || Math.abs(this.posY - (double) bedLocation.getY()) > 2.0D || Math.abs(this.posZ - (double) bedLocation.getZ()) > 3.0D) {
                return EntityPlayer.EnumStatus.TOO_FAR_AWAY;
            }

            double d0 = 8.0D;
            double d1 = 5.0D;
            List<EntityMob> list = this.worldObj.getEntitiesWithinAABB(EntityMob.class, new AxisAlignedBB((double) bedLocation.getX() - d0, (double) bedLocation.getY() - d1, (double) bedLocation.getZ() - d0, (double) bedLocation.getX() + d0, (double) bedLocation.getY() + d1, (double) bedLocation.getZ() + d0));

            if (!list.isEmpty()) {
                return EntityPlayer.EnumStatus.NOT_SAFE;
            }
        }

        if (this.isRiding()) {
            this.mountEntity(null);
        }

        this.setSize(0.2F, 0.2F);

        if (this.worldObj.isBlockLoaded(bedLocation)) {
            EnumFacing enumfacing = this.worldObj.getBlockState(bedLocation).getValue(BlockDirectional.FACING);
            float f = 0.5F;
            float f1 = 0.5F;

            switch (enumfacing) {
                case SOUTH -> f1 = 0.9F;
                case NORTH -> f1 = 0.1F;
                case WEST -> f = 0.1F;
                case EAST -> f = 0.9F;
            }

            this.setRenderOffsetForSleep(enumfacing);
            this.setPosition((float) bedLocation.getX() + f, (float) bedLocation.getY() + 0.6875F, (float) bedLocation.getZ() + f1);
        } else {
            this.setPosition((float) bedLocation.getX() + 0.5F, (float) bedLocation.getY() + 0.6875F, (float) bedLocation.getZ() + 0.5F);
        }

        this.sleeping = true;
        this.sleepTimer = 0;
        this.playerLocation = bedLocation;
        this.motionX = this.motionZ = this.motionY = 0.0D;

        if (!this.worldObj.isRemote) {
            this.worldObj.updateAllPlayersSleepingFlag();
        }

        return EntityPlayer.EnumStatus.OK;
    }

    private void setRenderOffsetForSleep(EnumFacing bedDirection) {
        this.renderOffsetX = 0.0F;
        this.renderOffsetZ = 0.0F;

        switch (bedDirection) {
            case SOUTH -> this.renderOffsetZ = -1.8F;
            case NORTH -> this.renderOffsetZ = 1.8F;
            case WEST -> this.renderOffsetX = 1.8F;
            case EAST -> this.renderOffsetX = -1.8F;
        }
    }

    public void wakeUpPlayer(boolean immediately, boolean updateWorldFlag, boolean setSpawn) {
        this.setSize(0.6F, 1.8F);
        IBlockState iblockstate = this.worldObj.getBlockState(this.playerLocation);

        if (this.playerLocation != null && iblockstate.getBlock() == Blocks.bed) {
            this.worldObj.setBlockState(this.playerLocation, iblockstate.withProperty(BlockBed.OCCUPIED, false), 4);
            BlockPos blockpos = BlockBed.getSafeExitLocation(this.worldObj, this.playerLocation, 0);

            if (blockpos == null) {
                blockpos = this.playerLocation.up();
            }

            this.setPosition((float) blockpos.getX() + 0.5F, (float) blockpos.getY() + 0.1F, (float) blockpos.getZ() + 0.5F);
        }

        this.sleeping = false;

        if (!this.worldObj.isRemote && updateWorldFlag) {
            this.worldObj.updateAllPlayersSleepingFlag();
        }

        this.sleepTimer = immediately ? 0 : 100;

        if (setSpawn) {
            this.setSpawnPoint(this.playerLocation, false);
        }
    }

    private boolean isInBed() {
        return this.worldObj.getBlockState(this.playerLocation).getBlock() == Blocks.bed;
    }

    public float getBedOrientationInDegrees() {
        if (this.playerLocation != null) {
            EnumFacing enumfacing = this.worldObj.getBlockState(this.playerLocation).getValue(BlockDirectional.FACING);

            switch (enumfacing) {
                case SOUTH:
                    return 90.0F;

                case NORTH:
                    return 270.0F;

                case WEST:
                    return 0.0F;

                case EAST:
                    return 180.0F;
            }
        }

        return 0.0F;
    }

    public boolean isPlayerSleeping() {
        return this.sleeping;
    }

    public boolean isPlayerFullyAsleep() {
        return this.sleeping && this.sleepTimer >= 100;
    }

    public int getSleepTimer() {
        return this.sleepTimer;
    }

    public void addChatComponentMessage(IChatComponent chatComponent) {
    }

    public BlockPos getBedLocation() {
        return this.spawnChunk;
    }

    public boolean isSpawnForced() {
        return this.spawnForced;
    }

    public void setSpawnPoint(BlockPos pos, boolean forced) {
        if (pos != null) {
            this.spawnChunk = pos;
            this.spawnForced = forced;
        } else {
            this.spawnChunk = null;
            this.spawnForced = false;
        }
    }

    public void triggerAchievement(StatBase achievementIn) {
        this.addStat(achievementIn, 1);
    }

    public void addStat(StatBase stat, int amount) {
    }

    public void takeStat(StatBase stat) {
    }

    public void tryJump(){
        super.jump();
    }

    public void jump() {
        super.jump();
        this.triggerAchievement(StatList.jumpStat);

        if (this.isSprinting()) {
            this.addExhaustion(0.8F);
        } else {
            this.addExhaustion(0.2F);
        }
    }

    public void moveEntityWithHeading(float strafe, float forward) {
        double d0 = this.posX;
        double d1 = this.posY;
        double d2 = this.posZ;

        if (this.capabilities.isFlying && this.ridingEntity == null) {
            double d3 = this.motionY;
            float f = this.jumpMovementFactor;
            this.jumpMovementFactor = this.capabilities.getFlySpeed() * (float) (this.isSprinting() ? 2 : 1);
            super.moveEntityWithHeading(strafe, forward);
            this.motionY = d3 * 0.6D;
            this.jumpMovementFactor = f;
        } else {
            super.moveEntityWithHeading(strafe, forward);
        }

        this.addMovementStat(this.posX - d0, this.posY - d1, this.posZ - d2);
    }

    public float getAIMoveSpeed() {
        return (float) this.getEntityAttribute(SharedMonsterAttributes.movementSpeed).getAttributeValue();
    }

    public void addMovementStat(double deltaX, double deltaY, double deltaZ) {
        if (this.ridingEntity == null) {
            if (this.isInsideOfMaterial(Material.water)) {
                int i = Math.round(MathHelper.sqrt_double(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ) * 100.0F);

                if (i > 0) {
                    this.addStat(StatList.distanceDoveStat, i);
                    this.addExhaustion(0.015F * (float) i * 0.01F);
                }
            } else if (this.isInWater()) {
                int j = Math.round(MathHelper.sqrt_double(deltaX * deltaX + deltaZ * deltaZ) * 100.0F);

                if (j > 0) {
                    this.addStat(StatList.distanceSwumStat, j);
                    this.addExhaustion(0.015F * (float) j * 0.01F);
                }
            } else if (this.isOnLadder()) {
                if (deltaY > 0.0D) {
                    this.addStat(StatList.distanceClimbedStat, (int) Math.round(deltaY * 100.0D));
                }
            } else if (this.onGround) {
                int k = Math.round(MathHelper.sqrt_double(deltaX * deltaX + deltaZ * deltaZ) * 100.0F);

                if (k > 0) {
                    this.addStat(StatList.distanceWalkedStat, k);

                    if (this.isSprinting()) {
                        this.addStat(StatList.distanceSprintedStat, k);
                        this.addExhaustion(0.099999994F * (float) k * 0.01F);
                    } else {
                        if (this.isSneaking()) {
                            this.addStat(StatList.distanceCrouchedStat, k);
                        }

                        this.addExhaustion(0.01F * (float) k * 0.01F);
                    }
                }
            } else {
                int l = Math.round(MathHelper.sqrt_double(deltaX * deltaX + deltaZ * deltaZ) * 100.0F);

                if (l > 25) {
                    this.addStat(StatList.distanceFlownStat, l);
                }
            }
        }
    }

    private void addMountedMovementStat(double deltaX, double deltaY, double deltaZ) {
        if (this.ridingEntity != null) {
            int i = Math.round(MathHelper.sqrt_double(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ) * 100.0F);

            if (i > 0) {
                if (this.ridingEntity instanceof EntityMinecart) {
                    this.addStat(StatList.distanceByMinecartStat, i);

                    if (this.startMinecartRidingCoordinate == null) {
                        this.startMinecartRidingCoordinate = new BlockPos(this);
                    } else if (this.startMinecartRidingCoordinate.distanceSq(MathHelper.floor_double(this.posX), MathHelper.floor_double(this.posY), MathHelper.floor_double(this.posZ)) >= 1000000.0D) {
                        this.triggerAchievement(AchievementList.onARail);
                    }
                } else if (this.ridingEntity instanceof EntityBoat) {
                    this.addStat(StatList.distanceByBoatStat, i);
                } else if (this.ridingEntity instanceof EntityPig) {
                    this.addStat(StatList.distanceByPigStat, i);
                } else if (this.ridingEntity instanceof EntityHorse) {
                    this.addStat(StatList.distanceByHorseStat, i);
                }
            }
        }
    }

    public void fall(float distance, float damageMultiplier) {
        if (!this.capabilities.allowFlying) {
            if (distance >= 2.0F) {
                this.addStat(StatList.distanceFallenStat, (int) Math.round((double) distance * 100.0D));
            }

            super.fall(distance, damageMultiplier);
        }
    }

    protected void resetHeight() {
        if (!this.isSpectator()) {
            super.resetHeight();
        }
    }

    protected String getFallSoundString(int damageValue) {
        return damageValue > 4 ? "game.player.hurt.fall.big" : "game.player.hurt.fall.small";
    }

    public void onKillEntity(EntityLivingBase entityLivingIn) {
        if (entityLivingIn instanceof IMob) {
            this.triggerAchievement(AchievementList.killEnemy);
        }

        EntityList.EntityEggInfo entitylist$entityegginfo = EntityList.entityEggs.get(EntityList.getEntityID(entityLivingIn));

        if (entitylist$entityegginfo != null) {
            this.triggerAchievement(entitylist$entityegginfo.killEntityStat);
        }
    }

    public void setInWeb() {
        if (!this.capabilities.isFlying) {
            super.setInWeb();
        }
    }

    public ItemStack getCurrentArmor(int slotIn) {
        return this.inventory.armorItemInSlot(slotIn);
    }

    public void addExperience(int amount) {
        this.addScore(amount);
        int i = Integer.MAX_VALUE - this.experienceTotal;

        if (amount > i) {
            amount = i;
        }

        this.experience += (float) amount / (float) this.xpBarCap();

        for (this.experienceTotal += amount; this.experience >= 1.0F; this.experience /= (float) this.xpBarCap()) {
            this.experience = (this.experience - 1.0F) * (float) this.xpBarCap();
            this.addExperienceLevel(1);
        }
    }

    public int getXPSeed() {
        return this.xpSeed;
    }

    public void removeExperienceLevel(int levels) {
        this.experienceLevel -= levels;

        if (this.experienceLevel < 0) {
            this.experienceLevel = 0;
            this.experience = 0.0F;
            this.experienceTotal = 0;
        }

        this.xpSeed = this.rand.nextInt();
    }

    public void addExperienceLevel(int levels) {
        this.experienceLevel += levels;

        if (this.experienceLevel < 0) {
            this.experienceLevel = 0;
            this.experience = 0.0F;
            this.experienceTotal = 0;
        }

        if (levels > 0 && this.experienceLevel % 5 == 0 && (float) this.lastXPSound < (float) this.ticksExisted - 100.0F) {
            float f = this.experienceLevel > 30 ? 1.0F : (float) this.experienceLevel / 30.0F;
            this.worldObj.playSoundAtEntity(this, "random.levelup", f * 0.75F, 1.0F);
            this.lastXPSound = this.ticksExisted;
        }
    }

    public int xpBarCap() {
        return this.experienceLevel >= 30 ? 112 + (this.experienceLevel - 30) * 9 : (this.experienceLevel >= 15 ? 37 + (this.experienceLevel - 15) * 5 : 7 + this.experienceLevel * 2);
    }

    public void addExhaustion(float exhaustion) {
        if (!this.capabilities.disableDamage) {
            if (!this.worldObj.isRemote) {
                this.foodStats.addExhaustion(exhaustion);
            }
        }
    }

    public FoodStats getFoodStats() {
        return this.foodStats;
    }

    public boolean canEat(boolean ignoreHunger) {
        return (ignoreHunger || this.foodStats.needFood()) && !this.capabilities.disableDamage;
    }

    public boolean shouldHeal() {
        return this.getHealth() > 0.0F && this.getHealth() < this.getMaxHealth();
    }

    public void setItemInUse(ItemStack stack, int duration) {
        if (stack != this.itemInUse) {
            this.itemInUse = stack;
            this.itemInUseCount = duration;

            if (!this.worldObj.isRemote) {
                this.setEating(true);
            }
        }
    }

    public boolean isAllowEdit() {
        return this.capabilities.allowEdit;
    }

    public boolean canPlayerEdit(BlockPos pos, EnumFacing facing, ItemStack stack) {
        if (this.capabilities.allowEdit) {
            return true;
        } else if (stack == null) {
            return false;
        } else {
            BlockPos blockpos = pos.offset(facing.getOpposite());
            Block block = this.worldObj.getBlockState(blockpos).getBlock();
            return stack.canPlaceOn(block) || stack.canEditBlocks();
        }
    }

    protected int getExperiencePoints(EntityPlayer player) {
        if (this.worldObj.getGameRules().getBoolean("keepInventory")) {
            return 0;
        } else {
            int i = this.experienceLevel * 7;
            return Math.min(i, 100);
        }
    }

    protected boolean isPlayer() {
        return true;
    }

    public boolean getAlwaysRenderNameTagForRender() {
        return true;
    }

    public void clonePlayer(EntityPlayer oldPlayer, boolean respawnFromEnd) {
        if (respawnFromEnd) {
            this.inventory.copyInventory(oldPlayer.inventory);
            this.setHealth(oldPlayer.getHealth());
            this.foodStats = oldPlayer.foodStats;
            this.experienceLevel = oldPlayer.experienceLevel;
            this.experienceTotal = oldPlayer.experienceTotal;
            this.experience = oldPlayer.experience;
            this.setScore(oldPlayer.getScore());
            this.lastPortalPos = oldPlayer.lastPortalPos;
            this.lastPortalVec = oldPlayer.lastPortalVec;
            this.teleportDirection = oldPlayer.teleportDirection;
        } else if (this.worldObj.getGameRules().getBoolean("keepInventory")) {
            this.inventory.copyInventory(oldPlayer.inventory);
            this.experienceLevel = oldPlayer.experienceLevel;
            this.experienceTotal = oldPlayer.experienceTotal;
            this.experience = oldPlayer.experience;
            this.setScore(oldPlayer.getScore());
        }

        this.xpSeed = oldPlayer.xpSeed;
        this.theInventoryEnderChest = oldPlayer.theInventoryEnderChest;
        this.getDataWatcher().updateObject(10, oldPlayer.getDataWatcher().getWatchableObjectByte(10));
    }

    protected boolean canTriggerWalking() {
        return !this.capabilities.isFlying;
    }

    public void sendPlayerAbilities() {
    }

    public void setGameType(WorldSettings.GameType gameType) {
    }

    public String getName() {
        return this.gameProfile.getName();
    }

    public InventoryEnderChest getInventoryEnderChest() {
        return this.theInventoryEnderChest;
    }

    public ItemStack getEquipmentInSlot(int slotIn) {
        return slotIn == 0 ? this.inventory.getCurrentItemStack() : this.inventory.armorInventory[slotIn - 1];
    }

    public ItemStack getHeldItem() {
        return this.inventory.getCurrentItemStack();
    }

    public void setCurrentItemOrArmor(int slotIn, ItemStack stack) {
        this.inventory.armorInventory[slotIn] = stack;
    }

    public boolean isInvisibleToPlayer(EntityPlayer player) {
        if (!this.isInvisible()) {
            return false;
        } else if (player.isSpectator()) {
            return false;
        } else {
            Team team = this.getTeam();
            return team == null || player.getTeam() != team || !team.getSeeFriendlyInvisiblesEnabled();
        }
    }

    public abstract boolean isSpectator();

    public ItemStack[] getInventory() {
        return this.inventory.armorInventory;
    }

    public boolean isPushedByWater() {
        return !this.capabilities.isFlying;
    }

    public Scoreboard getWorldScoreboard() {
        return this.worldObj.getScoreboard();
    }

    public Team getTeam() {
        return this.getWorldScoreboard().getPlayersTeam(this.getName());
    }

    public IChatComponent getDisplayName() {
        IChatComponent ichatcomponent = new ChatComponentText(ScorePlayerTeam.formatPlayerName(this.getTeam(), this.getName()));
        ichatcomponent.getChatStyle().setChatClickEvent(new ClickEvent(ClickEvent.Action.SUGGEST_COMMAND, "/msg " + this.getName() + " "));
        ichatcomponent.getChatStyle().setChatHoverEvent(this.getHoverEvent());
        ichatcomponent.getChatStyle().setInsertion(this.getName());
        return ichatcomponent;
    }

    public float getEyeHeight() {
        float f = 1.62F;

        if (this.isPlayerSleeping()) {
            f = 0.2F;
        }

        if (this.isSneaking()) {
            f -= 0.08F;
        }

        return f;
    }

    public float getAbsorptionAmount() {
        return this.getDataWatcher().getWatchableObjectFloat(17);
    }

    public void setAbsorptionAmount(float amount) {
        if (amount < 0.0F) {
            amount = 0.0F;
        }

        this.getDataWatcher().updateObject(17, amount);
    }

    public boolean canOpen(LockCode code) {
        if (code.isEmpty()) {
            return true;
        } else {
            ItemStack itemstack = this.getCurrentEquippedItem();
            return itemstack != null && itemstack.hasDisplayName() && itemstack.getDisplayName().equals(code.getLock());
        }
    }

    public boolean isWearing(EnumPlayerModelParts part) {
        return (this.getDataWatcher().getWatchableObjectByte(10) & part.getPartMask()) == part.getPartMask();
    }

    public boolean sendCommandFeedback() {
        return MinecraftServer.getServer().worldServers[0].getGameRules().getBoolean("sendCommandFeedback");
    }

    public boolean replaceItemInInventory(int inventorySlot, ItemStack itemStackIn) {
        if (inventorySlot >= 0 && inventorySlot < this.inventory.mainInventory.length) {
            this.inventory.setInventorySlotContents(inventorySlot, itemStackIn);
            return true;
        } else {
            int i = inventorySlot - 100;

            if (i >= 0 && i < this.inventory.armorInventory.length) {
                int k = i + 1;

                if (itemStackIn != null && itemStackIn.getItem() != null) {
                    if (itemStackIn.getItem() instanceof ItemArmor) {
                        if (EntityLiving.getArmorPosition(itemStackIn) != k) {
                            return false;
                        }
                    } else if (k != 4 || itemStackIn.getItem() != Items.skull && !(itemStackIn.getItem() instanceof ItemBlock)) {
                        return false;
                    }
                }

                this.inventory.setInventorySlotContents(i + this.inventory.mainInventory.length, itemStackIn);
                return true;
            } else {
                int j = inventorySlot - 200;

                if (j >= 0 && j < this.theInventoryEnderChest.getSizeInventory()) {
                    this.theInventoryEnderChest.setInventorySlotContents(j, itemStackIn);
                    return true;
                } else {
                    return false;
                }
            }
        }
    }

    public boolean hasReducedDebug() {
        return this.hasReducedDebug;
    }

    public void setReducedDebug(boolean reducedDebug) {
        this.hasReducedDebug = reducedDebug;
    }

    // Start of BloomClient
    public Vec3 getEyeVec3() {
        return new Vec3(this.posX, this.getEntityBoundingBox().minY + this.getEyeHeight(), this.posZ);
    }

    // End of BloomClient

    public enum EnumChatVisibility {
        FULL(0, "options.chat.visibility.full"),
        SYSTEM(1, "options.chat.visibility.system"),
        HIDDEN(2, "options.chat.visibility.hidden");

        private static final EntityPlayer.EnumChatVisibility[] ID_LOOKUP = new EntityPlayer.EnumChatVisibility[values().length];

        static {
            for (EntityPlayer.EnumChatVisibility entityplayer$enumchatvisibility : values()) {
                ID_LOOKUP[entityplayer$enumchatvisibility.chatVisibility] = entityplayer$enumchatvisibility;
            }
        }

        private final int chatVisibility;
        private final String resourceKey;

        EnumChatVisibility(int id, String resourceKey) {
            this.chatVisibility = id;
            this.resourceKey = resourceKey;
        }

        public static EntityPlayer.EnumChatVisibility getEnumChatVisibility(int id) {
            return ID_LOOKUP[id % ID_LOOKUP.length];
        }

        public int getChatVisibility() {
            return this.chatVisibility;
        }

        public String getResourceKey() {
            return this.resourceKey;
        }
    }

    // Start of BloomClient
    @Override
    public int getPing() {
        NetworkPlayerInfo playerInfo = Minecraft.getMinecraft().getNetHandler().getPlayerInfo(this.getUniqueID());

        if (playerInfo != null) {
            int responseTime = playerInfo.getResponseTime();
            if (responseTime < 0)
                return 0;

            return responseTime;
        }

        return -1;
    }

    @Override
    public ResourceLocation getSkin() {
        NetworkPlayerInfo playerInfo = Minecraft.getMinecraft().getNetHandler().getPlayerInfo(this.getUniqueID());

        if (playerInfo != null) {
            return playerInfo.getLocationSkin();
        }

        return DefaultPlayerSkin.getDefaultSkinLegacy();
    }
    // End of BloomClient

    public enum EnumStatus {
        OK,
        NOT_POSSIBLE_HERE,
        NOT_POSSIBLE_NOW,
        TOO_FAR_AWAY,
        OTHER_PROBLEM,
        NOT_SAFE
    }
}
