package net.minecraft.world.gen;

import java.util.Arrays;
import java.util.Random;

public class NoiseGeneratorPerlin extends NoiseGenerator {
    private final NoiseGeneratorSimplex[] noiseLevels;
    private final int levels;

    public NoiseGeneratorPerlin(Random seed, int levelsIn) {
        this.levels = levelsIn;
        this.noiseLevels = new NoiseGeneratorSimplex[levelsIn];

        for (int i = 0; i < levelsIn; ++i) {
            this.noiseLevels[i] = new NoiseGeneratorSimplex(seed);
        }
    }

    public double getValue(double x, double z) {
        double d0 = 0.0D;
        double d1 = 1.0D;

        for (int i = 0; i < this.levels; ++i) {
            d0 += this.noiseLevels[i].getValue(x * d1, z * d1) / d1;
            d1 /= 2.0D;
        }

        return d0;
    }

    public double[] getRegion(double[] regionData, double x, double y, int width, int height, double scaleX, double scaleY, double persistence) {
        return this.getRegion(regionData, x, y, width, height, scaleX, scaleY, persistence, 0.5D);
    }

    public double[] getRegion(double[] regionData, double x, double y, int width, int height, double scaleX, double scaleY, double persistence, double frequency) {
        if (regionData != null && regionData.length >= width * height) {
            Arrays.fill(regionData, 0.0D);
        } else {
            regionData = new double[width * height];
        }

        double d1 = 1.0D;
        double d0 = 1.0D;

        for (int j = 0; j < this.levels; ++j) {
            this.noiseLevels[j].add(regionData, x, y, width, height, scaleX * d0 * d1, scaleY * d0 * d1, 0.55D / d1);
            d0 *= persistence;
            d1 *= frequency;
        }

        return regionData;
    }
}
