package net.minecraft.world.gen.feature;

import net.minecraft.block.BlockVine;
import net.minecraft.block.properties.PropertyBool;
import net.minecraft.block.state.IBlockState;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.MathHelper;
import net.minecraft.world.World;

import java.util.Random;

public class WorldGenMegaJungle extends WorldGenHugeTrees {
    public WorldGenMegaJungle(boolean notify, int baseHeightIn, int extraRandomHeightIn, IBlockState woodMetadataIn, IBlockState leavesMetadataIn) {
        super(notify, baseHeightIn, extraRandomHeightIn, woodMetadataIn, leavesMetadataIn);
    }

    public boolean generate(World worldIn, Random rand, BlockPos position) {
        int i = this.getHeight(rand);

        if (!this.ensureGrowable(worldIn, rand, position, i)) {
            return false;
        } else {
            this.createCrown(worldIn, position.up(i), 2);

            for (int j = position.getY() + i - 2 - rand.nextInt(4); j > position.getY() + i / 2; j -= 2 + rand.nextInt(4)) {
                float f = rand.nextFloat() * (float) Math.PI * 2.0F;
                int k = position.getX() + (int) (0.5F + MathHelper.cos(f) * 4.0F);
                int l = position.getZ() + (int) (0.5F + MathHelper.sin(f) * 4.0F);

                for (int i1 = 0; i1 < 5; ++i1) {
                    k = position.getX() + (int) (1.5F + MathHelper.cos(f) * (float) i1);
                    l = position.getZ() + (int) (1.5F + MathHelper.sin(f) * (float) i1);
                    this.setBlockAndNotifyAdequately(worldIn, new BlockPos(k, j - 3 + i1 / 2, l), this.woodMetadata);
                }

                int j2 = 1 + rand.nextInt(2);

                for (int k1 = j - j2; k1 <= j; ++k1) {
                    int l1 = k1 - j;
                    this.growLeavesLayer(worldIn, new BlockPos(k, k1, l), 1 - l1);
                }
            }

            for (int i2 = 0; i2 < i; ++i2) {
                BlockPos blockpos = position.up(i2);

                if (this.canGrowInto(worldIn.getBlockState(blockpos).getBlock())) {
                    this.setBlockAndNotifyAdequately(worldIn, blockpos, this.woodMetadata);

                    if (i2 > 0) {
                        this.placeVine(worldIn, rand, blockpos.west(), BlockVine.EAST);
                        this.placeVine(worldIn, rand, blockpos.north(), BlockVine.SOUTH);
                    }
                }

                if (i2 < i - 1) {
                    BlockPos blockpos1 = blockpos.east();

                    if (this.canGrowInto(worldIn.getBlockState(blockpos1).getBlock())) {
                        this.setBlockAndNotifyAdequately(worldIn, blockpos1, this.woodMetadata);

                        if (i2 > 0) {
                            this.placeVine(worldIn, rand, blockpos1.east(), BlockVine.WEST);
                            this.placeVine(worldIn, rand, blockpos1.north(), BlockVine.SOUTH);
                        }
                    }

                    BlockPos blockpos2 = blockpos.south().east();

                    if (this.canGrowInto(worldIn.getBlockState(blockpos2).getBlock())) {
                        this.setBlockAndNotifyAdequately(worldIn, blockpos2, this.woodMetadata);

                        if (i2 > 0) {
                            this.placeVine(worldIn, rand, blockpos2.east(), BlockVine.WEST);
                            this.placeVine(worldIn, rand, blockpos2.south(), BlockVine.NORTH);
                        }
                    }

                    BlockPos blockpos3 = blockpos.south();

                    if (this.canGrowInto(worldIn.getBlockState(blockpos3).getBlock())) {
                        this.setBlockAndNotifyAdequately(worldIn, blockpos3, this.woodMetadata);

                        if (i2 > 0) {
                            this.placeVine(worldIn, rand, blockpos3.west(), BlockVine.EAST);
                            this.placeVine(worldIn, rand, blockpos3.south(), BlockVine.NORTH);
                        }
                    }
                }
            }

            return true;
        }
    }

    private void placeVine(World worldIn, Random rand, BlockPos pos, PropertyBool boolProp) {
        if (rand.nextInt(3) > 0 && worldIn.isAirBlock(pos)) {
            this.setBlockAndNotifyAdequately(worldIn, pos, Blocks.vine.getDefaultState().withProperty(boolProp, true));
        }
    }

    private void createCrown(World worldIn, BlockPos pos, int height) {
        int i = 2;

        for (int j = -i; j <= 0; ++j) {
            this.growLeavesLayerStrict(worldIn, pos.up(j), height + 1 - j);
        }
    }
}
