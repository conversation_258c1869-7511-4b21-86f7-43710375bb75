package net.minecraft.stats;

import com.google.common.collect.Maps;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.IJsonSerializable;
import net.minecraft.util.TupleIntJsonSerializable;

import java.util.Map;

public class StatFileWriter {
    protected final Map<StatBase, TupleIntJsonSerializable> statsData = Maps.newConcurrentMap();

    public boolean hasAchievementUnlocked(Achievement achievementIn) {
        return this.readStat(achievementIn) > 0;
    }

    public boolean canUnlockAchievement(Achievement achievementIn) {
        return achievementIn.parentAchievement == null || this.hasAchievementUnlocked(achievementIn.parentAchievement);
    }

    public int countRequirementsUntilAvailable(Achievement achievementIn) {
        if (this.hasAchievementUnlocked(achievementIn)) {
            return 0;
        } else {
            int i = 0;

            for (Achievement achievement = achievementIn.parentAchievement; achievement != null && !this.hasAchievementUnlocked(achievement); ++i) {
                achievement = achievement.parentAchievement;
            }

            return i;
        }
    }

    public void increaseStat(EntityPlayer player, StatBase stat, int amount) {
        if (!stat.isAchievement() || this.canUnlockAchievement((Achievement) stat)) {
            this.unlockAchievement(player, stat, this.readStat(stat) + amount);
        }
    }

    public void unlockAchievement(EntityPlayer playerIn, StatBase statIn, int achievementValue) {
        TupleIntJsonSerializable tupleintjsonserializable = this.statsData.computeIfAbsent(statIn, k -> new TupleIntJsonSerializable());

        tupleintjsonserializable.setIntegerValue(achievementValue);
    }

    public int readStat(StatBase stat) {
        TupleIntJsonSerializable tupleintjsonserializable = this.statsData.get(stat);
        return tupleintjsonserializable == null ? 0 : tupleintjsonserializable.getIntegerValue();
    }

    public <T extends IJsonSerializable> T getProgress(StatBase stat) {
        TupleIntJsonSerializable tupleintjsonserializable = this.statsData.get(stat);
        return tupleintjsonserializable != null ? tupleintjsonserializable.getJsonSerializableValue() : null;
    }

    public <T extends IJsonSerializable> T setProgress(StatBase stat, T value) {
        TupleIntJsonSerializable tupleintjsonserializable = this.statsData.computeIfAbsent(stat, k -> new TupleIntJsonSerializable());

        tupleintjsonserializable.setJsonSerializableValue(value);
        return value;
    }
}
