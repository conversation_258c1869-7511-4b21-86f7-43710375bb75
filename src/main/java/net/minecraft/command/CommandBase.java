package net.minecraft.command;

import com.google.common.base.Functions;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.primitives.Doubles;
import net.minecraft.block.Block;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.item.Item;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.BlockPos;
import net.minecraft.util.ChatComponentText;
import net.minecraft.util.IChatComponent;
import net.minecraft.util.ResourceLocation;

import java.util.*;

public abstract class CommandBase implements ICommand {
    private static IAdminCommand theAdmin;

    public static int parseInt(String input) throws NumberInvalidException {
        try {
            return Integer.parseInt(input);
        } catch (NumberFormatException var2) {
            throw new NumberInvalidException("commands.generic.num.invalid", input);
        }
    }

    public static int parseInt(String input, int min) throws NumberInvalidException {
        return parseInt(input, min, Integer.MAX_VALUE);
    }

    public static int parseInt(String input, int min, int max) throws NumberInvalidException {
        int i = parseInt(input);

        if (i < min) {
            throw new NumberInvalidException("commands.generic.num.tooSmall", i, min);
        } else if (i > max) {
            throw new NumberInvalidException("commands.generic.num.tooBig", i, max);
        } else {
            return i;
        }
    }

    public static long parseLong(String input) throws NumberInvalidException {
        try {
            return Long.parseLong(input);
        } catch (NumberFormatException var2) {
            throw new NumberInvalidException("commands.generic.num.invalid", input);
        }
    }

    public static long parseLong(String input, long min, long max) throws NumberInvalidException {
        long i = parseLong(input);

        if (i < min) {
            throw new NumberInvalidException("commands.generic.num.tooSmall", i, min);
        } else if (i > max) {
            throw new NumberInvalidException("commands.generic.num.tooBig", i, max);
        } else {
            return i;
        }
    }

    public static BlockPos parseBlockPos(ICommandSender sender, String[] args, int startIndex, boolean centerBlock) throws NumberInvalidException {
        BlockPos blockpos = sender.getPosition();
        return new BlockPos(parseDouble(blockpos.getX(), args[startIndex], -30000000, 30000000, centerBlock), parseDouble(blockpos.getY(), args[startIndex + 1], 0, 256, false), parseDouble(blockpos.getZ(), args[startIndex + 2], -30000000, 30000000, centerBlock));
    }

    public static double parseDouble(String input) throws NumberInvalidException {
        try {
            double d0 = Double.parseDouble(input);

            if (!Doubles.isFinite(d0)) {
                throw new NumberInvalidException("commands.generic.num.invalid", input);
            } else {
                return d0;
            }
        } catch (NumberFormatException var3) {
            throw new NumberInvalidException("commands.generic.num.invalid", input);
        }
    }

    public static double parseDouble(String input, double min) throws NumberInvalidException {
        return parseDouble(input, min, Double.MAX_VALUE);
    }

    public static double parseDouble(String input, double min, double max) throws NumberInvalidException {
        double d0 = parseDouble(input);

        if (d0 < min) {
            throw new NumberInvalidException("commands.generic.double.tooSmall", d0, min);
        } else if (d0 > max) {
            throw new NumberInvalidException("commands.generic.double.tooBig", d0, max);
        } else {
            return d0;
        }
    }

    public static boolean parseBoolean(String input) throws CommandException {
        if (!input.equals("true") && !input.equals("1")) {
            if (!input.equals("false") && !input.equals("0")) {
                throw new CommandException("commands.generic.boolean.invalid", input);
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    public static EntityPlayerMP getCommandSenderAsPlayer(ICommandSender sender) throws PlayerNotFoundException {
        if (sender instanceof EntityPlayerMP) {
            return (EntityPlayerMP) sender;
        } else {
            throw new PlayerNotFoundException("You must specify which player you wish to perform this action on.");
        }
    }

    public static EntityPlayerMP getPlayer(ICommandSender sender, String username) throws PlayerNotFoundException {
        EntityPlayerMP entityplayermp = PlayerSelector.matchOnePlayer(sender, username);

        if (entityplayermp == null) {
            try {
                entityplayermp = MinecraftServer.getServer().getConfigurationManager().getPlayerByUUID(UUID.fromString(username));
            } catch (IllegalArgumentException ignored) {
            }
        }

        if (entityplayermp == null) {
            entityplayermp = MinecraftServer.getServer().getConfigurationManager().getPlayerByUsername(username);
        }

        if (entityplayermp == null) {
            throw new PlayerNotFoundException();
        } else {
            return entityplayermp;
        }
    }

    public static Entity getEntity(ICommandSender sender, String name) throws EntityNotFoundException {
        return getEntity(sender, name, Entity.class);
    }

    public static <T extends Entity> T getEntity(ICommandSender commandSender, String name, Class<? extends T> clazz) throws EntityNotFoundException {
        Entity entity = PlayerSelector.matchOneEntity(commandSender, name, clazz);
        MinecraftServer minecraftserver = MinecraftServer.getServer();

        if (entity == null) {
            entity = minecraftserver.getConfigurationManager().getPlayerByUsername(name);
        }

        if (entity == null) {
            try {
                UUID uuid = UUID.fromString(name);
                entity = minecraftserver.getEntityFromUuid(uuid);

                if (entity == null) {
                    entity = minecraftserver.getConfigurationManager().getPlayerByUUID(uuid);
                }
            } catch (IllegalArgumentException var6) {
                throw new EntityNotFoundException("commands.generic.entity.invalidUuid");
            }
        }

        if (entity != null && clazz.isAssignableFrom(entity.getClass())) {
            return (T) entity;
        } else {
            throw new EntityNotFoundException();
        }
    }

    public static List<Entity> getEntitiesMatchPattern(ICommandSender sender, String name) throws EntityNotFoundException {
        return PlayerSelector.hasArguments(name) ? PlayerSelector.matchEntities(sender, name, Entity.class) : Lists.newArrayList(getEntity(sender, name));
    }

    public static String getPlayerName(ICommandSender sender, String query) throws PlayerNotFoundException {
        try {
            return getPlayer(sender, query).getName();
        } catch (PlayerNotFoundException playernotfoundexception) {
            if (PlayerSelector.hasArguments(query)) {
                throw playernotfoundexception;
            } else {
                return query;
            }
        }
    }

    public static String getEntityName(ICommandSender sender, String name) throws EntityNotFoundException {
        try {
            return getPlayer(sender, name).getName();
        } catch (PlayerNotFoundException var5) {
            try {
                return getEntity(sender, name).getUniqueID().toString();
            } catch (EntityNotFoundException entitynotfoundexception) {
                if (PlayerSelector.hasArguments(name)) {
                    throw entitynotfoundexception;
                } else {
                    return name;
                }
            }
        }
    }

    public static IChatComponent getChatComponentFromNthArg(ICommandSender sender, String[] args, int index) throws CommandException {
        return getChatComponentFromNthArg(sender, args, index, false);
    }

    public static IChatComponent getChatComponentFromNthArg(ICommandSender sender, String[] args, int index, boolean selector) throws PlayerNotFoundException {
        IChatComponent ichatcomponent = new ChatComponentText("");

        for (int i = index; i < args.length; ++i) {
            if (i > index) {
                ichatcomponent.appendText(" ");
            }

            IChatComponent ichatcomponent1 = new ChatComponentText(args[i]);

            if (selector) {
                IChatComponent ichatcomponent2 = PlayerSelector.matchEntitiesToChatComponent(sender, args[i]);

                if (ichatcomponent2 == null) {
                    if (PlayerSelector.hasArguments(args[i])) {
                        throw new PlayerNotFoundException();
                    }
                } else {
                    ichatcomponent1 = ichatcomponent2;
                }
            }

            ichatcomponent.appendSibling(ichatcomponent1);
        }

        return ichatcomponent;
    }

    public static String buildString(String[] args, int startPos) {
        StringBuilder stringbuilder = new StringBuilder();

        for (int i = startPos; i < args.length; ++i) {
            if (i > startPos) {
                stringbuilder.append(" ");
            }

            String s = args[i];
            stringbuilder.append(s);
        }

        return stringbuilder.toString();
    }

    public static CommandBase.CoordinateArg parseCoordinate(double base, String selectorArg, boolean centerBlock) throws NumberInvalidException {
        return parseCoordinate(base, selectorArg, -30000000, 30000000, centerBlock);
    }

    public static CommandBase.CoordinateArg parseCoordinate(double base, String selectorArg, int min, int max, boolean centerBlock) throws NumberInvalidException {
        boolean flag = selectorArg.startsWith("~");

        if (flag && Double.isNaN(base)) {
            throw new NumberInvalidException("commands.generic.num.invalid", base);
        } else {
            double d0 = 0.0D;

            if (!flag || selectorArg.length() > 1) {
                boolean flag1 = selectorArg.contains(".");

                if (flag) {
                    selectorArg = selectorArg.substring(1);
                }

                d0 += parseDouble(selectorArg);

                if (!flag1 && !flag && centerBlock) {
                    d0 += 0.5D;
                }
            }

            if (min != 0 || max != 0) {
                if (d0 < (double) min) {
                    throw new NumberInvalidException("commands.generic.double.tooSmall", d0, min);
                }

                if (d0 > (double) max) {
                    throw new NumberInvalidException("commands.generic.double.tooBig", d0, max);
                }
            }

            return new CommandBase.CoordinateArg(d0 + (flag ? base : 0.0D), d0, flag);
        }
    }

    public static double parseDouble(double base, String input, boolean centerBlock) throws NumberInvalidException {
        return parseDouble(base, input, -30000000, 30000000, centerBlock);
    }

    public static double parseDouble(double base, String input, int min, int max, boolean centerBlock) throws NumberInvalidException {
        boolean flag = input.startsWith("~");

        if (flag && Double.isNaN(base)) {
            throw new NumberInvalidException("commands.generic.num.invalid", base);
        } else {
            double d0 = flag ? base : 0.0D;

            if (!flag || input.length() > 1) {
                boolean flag1 = input.contains(".");

                if (flag) {
                    input = input.substring(1);
                }

                d0 += parseDouble(input);

                if (!flag1 && !flag && centerBlock) {
                    d0 += 0.5D;
                }
            }

            if (min != 0 || max != 0) {
                if (d0 < (double) min) {
                    throw new NumberInvalidException("commands.generic.double.tooSmall", d0, min);
                }

                if (d0 > (double) max) {
                    throw new NumberInvalidException("commands.generic.double.tooBig", d0, max);
                }
            }

            return d0;
        }
    }

    public static Item getItemByText(ICommandSender sender, String id) throws NumberInvalidException {
        ResourceLocation resourcelocation = new ResourceLocation(id);
        Item item = Item.itemRegistry.getObject(resourcelocation);

        if (item == null) {
            throw new NumberInvalidException("commands.give.item.notFound", resourcelocation);
        } else {
            return item;
        }
    }

    public static Block getBlockByText(ICommandSender sender, String id) throws NumberInvalidException {
        ResourceLocation resourcelocation = new ResourceLocation(id);

        if (!Block.blockRegistry.containsKey(resourcelocation)) {
            throw new NumberInvalidException("commands.give.block.notFound", resourcelocation);
        } else {
            Block block = Block.blockRegistry.getObject(resourcelocation);

            if (block == null) {
                throw new NumberInvalidException("commands.give.block.notFound", resourcelocation);
            } else {
                return block;
            }
        }
    }

    public static String joinNiceString(Object[] elements) {
        StringBuilder stringbuilder = new StringBuilder();

        for (int i = 0; i < elements.length; ++i) {
            String s = elements[i].toString();

            if (i > 0) {
                if (i == elements.length - 1) {
                    stringbuilder.append(" and ");
                } else {
                    stringbuilder.append(", ");
                }
            }

            stringbuilder.append(s);
        }

        return stringbuilder.toString();
    }

    public static IChatComponent join(List<IChatComponent> components) {
        IChatComponent ichatcomponent = new ChatComponentText("");

        for (int i = 0; i < components.size(); ++i) {
            if (i > 0) {
                if (i == components.size() - 1) {
                    ichatcomponent.appendText(" and ");
                } else {
                    ichatcomponent.appendText(", ");
                }
            }

            ichatcomponent.appendSibling(components.get(i));
        }

        return ichatcomponent;
    }

    public static String joinNiceStringFromCollection(Collection<String> strings) {
        return joinNiceString(strings.toArray(new String[0]));
    }

    public static List<String> getTabCompletionCoordinate(String[] inputArgs, int index, BlockPos pos) {
        if (pos == null) {
            return null;
        } else {
            int i = inputArgs.length - 1;
            String s;

            if (i == index) {
                s = Integer.toString(pos.getX());
            } else if (i == index + 1) {
                s = Integer.toString(pos.getY());
            } else {
                if (i != index + 2) {
                    return null;
                }

                s = Integer.toString(pos.getZ());
            }

            return Lists.newArrayList(s);
        }
    }

    public static List<String> getTabCompletionCoordinateXZ(String[] inputArgs, int index, BlockPos lookedPos) {
        if (lookedPos == null) {
            return null;
        } else {
            int i = inputArgs.length - 1;
            String s;

            if (i == index) {
                s = Integer.toString(lookedPos.getX());
            } else {
                if (i != index + 1) {
                    return null;
                }

                s = Integer.toString(lookedPos.getZ());
            }

            return Lists.newArrayList(s);
        }
    }

    public static boolean doesStringStartWith(String original, String region) {
        return region.regionMatches(true, 0, original, 0, original.length());
    }

    public static List<String> getListOfStringsMatchingLastWord(String[] args, String... possibilities) {
        return getListOfStringsMatchingLastWord(args, Arrays.asList(possibilities));
    }

    public static List<String> getListOfStringsMatchingLastWord(String[] inputArgs, Collection<?> possibleCompletions) {
        String s = inputArgs[inputArgs.length - 1];
        List<String> list = Lists.newArrayList();

        if (!possibleCompletions.isEmpty()) {
            for (String s1 : Iterables.transform(possibleCompletions, Functions.toStringFunction())) {
                if (doesStringStartWith(s, s1)) {
                    list.add(s1);
                }
            }

            if (list.isEmpty()) {
                for (Object object : possibleCompletions) {
                    if (object instanceof ResourceLocation && doesStringStartWith(s, ((ResourceLocation) object).getResourcePath())) {
                        list.add(String.valueOf(object));
                    }
                }
            }
        }

        return list;
    }

    public static void notifyOperators(ICommandSender sender, ICommand command, String msgFormat, Object... msgParams) {
        notifyOperators(sender, command, 0, msgFormat, msgParams);
    }

    public static void notifyOperators(ICommandSender sender, ICommand command, int flags, String msgFormat, Object... msgParams) {
        if (theAdmin != null) {
            theAdmin.notifyOperators(sender, command, flags, msgFormat, msgParams);
        }
    }

    public static void setAdminCommander(IAdminCommand command) {
        theAdmin = command;
    }

    public int getRequiredPermissionLevel() {
        return 4;
    }

    public List<String> getCommandAliases() {
        return Collections.emptyList();
    }

    public boolean canCommandSenderUseCommand(ICommandSender sender) {
        return sender.canCommandSenderUseCommand(this.getRequiredPermissionLevel(), this.getCommandName());
    }

    public List<String> addTabCompletionOptions(ICommandSender sender, String[] args, BlockPos pos) {
        return null;
    }

    public boolean isUsernameIndex(String[] args, int index) {
        return false;
    }

    public int compareTo(ICommand p_compareTo_1_) {
        return this.getCommandName().compareTo(p_compareTo_1_.getCommandName());
    }

    public static class CoordinateArg {
        private final double result;
        private final double amount;
        private final boolean isRelative;

        protected CoordinateArg(double resultIn, double amountIn, boolean relative) {
            this.result = resultIn;
            this.amount = amountIn;
            this.isRelative = relative;
        }

        public double getResult() {
            return this.result;
        }

        public double getAmount() {
            return this.amount;
        }

        public boolean isRelative() {
            return this.isRelative;
        }
    }
}
