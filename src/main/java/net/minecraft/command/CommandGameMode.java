package net.minecraft.command;

import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.BlockPos;
import net.minecraft.util.ChatComponentTranslation;
import net.minecraft.util.IChatComponent;
import net.minecraft.world.WorldSettings;

import java.util.List;

public class CommandGameMode extends CommandBase {
    public String getCommandName() {
        return "gamemode";
    }

    public int getRequiredPermissionLevel() {
        return 2;
    }

    public String getCommandUsage(ICommandSender sender) {
        return "commands.gamemode.usage";
    }

    public void processCommand(ICommandSender sender, String[] args) throws CommandException {
        if (args.length == 0) {
            throw new WrongUsageException("commands.gamemode.usage");
        } else {
            WorldSettings.GameType worldsettings$gametype = this.getGameModeFromCommand(sender, args[0]);
            EntityPlayer entityplayer = args.length >= 2 ? getPlayer(sender, args[1]) : getCommandSenderAsPlayer(sender);
            entityplayer.setGameType(worldsettings$gametype);
            entityplayer.fallDistance = 0.0F;

            if (sender.getEntityWorld().getGameRules().getBoolean("sendCommandFeedback")) {
                entityplayer.addChatMessage(new ChatComponentTranslation("gameMode.changed"));
            }

            IChatComponent ichatcomponent = new ChatComponentTranslation("gameMode." + worldsettings$gametype.getName());

            if (entityplayer != sender) {
                notifyOperators(sender, this, 1, "commands.gamemode.success.other", entityplayer.getName(), ichatcomponent);
            } else {
                notifyOperators(sender, this, 1, "commands.gamemode.success.self", ichatcomponent);
            }
        }
    }

    protected WorldSettings.GameType getGameModeFromCommand(ICommandSender sender, String gameModeString) throws CommandException {
        return !gameModeString.equalsIgnoreCase(WorldSettings.GameType.SURVIVAL.getName()) && !gameModeString.equalsIgnoreCase("s") ? (!gameModeString.equalsIgnoreCase(WorldSettings.GameType.CREATIVE.getName()) && !gameModeString.equalsIgnoreCase("c") ? (!gameModeString.equalsIgnoreCase(WorldSettings.GameType.ADVENTURE.getName()) && !gameModeString.equalsIgnoreCase("a") ? (!gameModeString.equalsIgnoreCase(WorldSettings.GameType.SPECTATOR.getName()) && !gameModeString.equalsIgnoreCase("sp") ? WorldSettings.getGameTypeById(parseInt(gameModeString, 0, WorldSettings.GameType.values().length - 2)) : WorldSettings.GameType.SPECTATOR) : WorldSettings.GameType.ADVENTURE) : WorldSettings.GameType.CREATIVE) : WorldSettings.GameType.SURVIVAL;
    }

    public List<String> addTabCompletionOptions(ICommandSender sender, String[] args, BlockPos pos) {
        return args.length == 1 ? getListOfStringsMatchingLastWord(args, "survival", "creative", "adventure", "spectator") : (args.length == 2 ? getListOfStringsMatchingLastWord(args, this.getListOfPlayerUsernames()) : null);
    }

    protected String[] getListOfPlayerUsernames() {
        return MinecraftServer.getServer().getAllUsernames();
    }

    public boolean isUsernameIndex(String[] args, int index) {
        return index == 1;
    }
}
