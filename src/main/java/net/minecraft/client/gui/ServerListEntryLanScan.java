package net.minecraft.client.gui;

import net.minecraft.client.Minecraft;
import net.minecraft.client.resources.I18n;

public class ServerListEntryLanScan implements GuiListExtended.IGuiListEntry {
    private final Minecraft mc = Minecraft.getMinecraft();

    public void drawEntry(int slotIndex, int x, int y, int listWidth, int slotHeight, int mouseX, int mouseY, boolean isSelected) {
        int i = y + slotHeight / 2 - this.mc.minecraftFontRendererObj.FONT_HEIGHT / 2;
        this.mc.minecraftFontRendererObj.drawString(I18n.format("lanServer.scanning"), this.mc.currentScreen.width / 2 - this.mc.minecraftFontRendererObj.getStringWidth(I18n.format("lanServer.scanning")) / 2, i, 16777215);
        String s = switch ((int) (Minecraft.getSystemTime() / 300L % 4L)) {
            case 1, 3 -> "o O o";
            default -> "O o o";
        };

        this.mc.minecraftFontRendererObj.drawString(s, this.mc.currentScreen.width / 2 - this.mc.minecraftFontRendererObj.getStringWidth(s) / 2, i + this.mc.minecraftFontRendererObj.FONT_HEIGHT, 8421504);
    }

    public void setSelected(int entryID, int insideLeft, int yPos) {
    }

    public boolean mousePressed(int slotIndex, int mouseX, int mouseY, int mouseEvent, int relativeX, int relativeY) {
        return false;
    }

    public void mouseReleased(int slotIndex, int x, int y, int mouseEvent, int relativeX, int relativeY) {
    }
}
