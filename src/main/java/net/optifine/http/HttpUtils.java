package net.optifine.http;

import net.minecraft.client.Minecraft;
import net.minecraft.src.Config;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class HttpUtils {
    private static String playerItemsUrl = null;

    public static byte[] get(String urlStr) throws IOException {
        HttpURLConnection httpurlconnection = null;
        byte[] abyte1;

        try {
            URL url = new URL(urlStr);
            httpurlconnection = (HttpURLConnection) url.openConnection(Minecraft.getMinecraft().getProxy());
            httpurlconnection.setDoInput(true);
            httpurlconnection.setDoOutput(false);
            httpurlconnection.connect();

            if (httpurlconnection.getResponseCode() / 100 != 2) {
                if (httpurlconnection.getErrorStream() != null) {
                    Config.readAll(httpurlconnection.getErrorStream());
                }

                throw new IOException("HTTP response: " + httpurlconnection.getResponseCode());
            }

            InputStream inputstream = httpurlconnection.getInputStream();
            byte[] abyte = new byte[httpurlconnection.getContentLength()];
            int i = 0;

            do {
                int j = inputstream.read(abyte, i, abyte.length - i);

                if (j < 0) {
                    throw new IOException("Input stream closed: " + urlStr);
                }

                i += j;

            } while (i < abyte.length);

            abyte1 = abyte;
        } finally {
            if (httpurlconnection != null) {
                httpurlconnection.disconnect();
            }
        }

        return abyte1;
    }

    public static void post(String urlStr, Map<String, String> headers, byte[] content) throws IOException {
        HttpURLConnection httpurlconnection = null;

        try {
            URL url = new URL(urlStr);
            httpurlconnection = (HttpURLConnection) url.openConnection(Minecraft.getMinecraft().getProxy());
            httpurlconnection.setRequestMethod("POST");

            if (headers != null) {
                for (String s : headers.keySet()) {
                    String s1 = headers.get(s);
                    httpurlconnection.setRequestProperty(s, s1);
                }
            }

            httpurlconnection.setRequestProperty("Content-Type", "text/plain");
            httpurlconnection.setRequestProperty("Content-Length", "" + content.length);
            httpurlconnection.setRequestProperty("Content-Language", "en-US");
            httpurlconnection.setUseCaches(false);
            httpurlconnection.setDoInput(true);
            httpurlconnection.setDoOutput(true);
            OutputStream outputstream = httpurlconnection.getOutputStream();
            outputstream.write(content);
            outputstream.flush();
            outputstream.close();
            InputStream inputstream = httpurlconnection.getInputStream();
            InputStreamReader inputstreamreader = new InputStreamReader(inputstream, StandardCharsets.US_ASCII);
            BufferedReader bufferedreader = new BufferedReader(inputstreamreader);
            StringBuilder stringbuffer = new StringBuilder();
            String s2;

            while ((s2 = bufferedreader.readLine()) != null) {
                stringbuffer.append(s2);
                stringbuffer.append('\r');
            }

            bufferedreader.close();
        } finally {
            if (httpurlconnection != null) {
                httpurlconnection.disconnect();
            }
        }

    }

    public static synchronized String getPlayerItemsUrl() {
        if (playerItemsUrl == null) {
            try {
                boolean flag = Config.parseBoolean(System.getProperty("player.models.local"), false);

                if (flag) {
                    File file1 = Minecraft.getMinecraft().mcDataDir;
                    File file2 = new File(file1, "playermodels");
                    playerItemsUrl = file2.toURI().toURL().toExternalForm();
                }
            } catch (Exception exception) {
                Config.warn(exception.getClass().getName() + ": " + exception.getMessage());
            }

            if (playerItemsUrl == null) {
                playerItemsUrl = "http://s.optifine.net";
            }
        }

        return playerItemsUrl;
    }
}
