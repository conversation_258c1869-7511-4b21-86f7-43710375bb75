package net.bloom.bloomclient.features.module.modules.world.scaffold.tower

import net.bloom.bloomclient.event.SentPacketEvent
import net.bloom.bloomclient.event.UpdateEvent
import net.bloom.bloomclient.features.component.components.player.PacketComponent
import net.bloom.bloomclient.features.mode.Mode
import net.bloom.bloomclient.utils.player.MovementUtils
import net.bloom.bloomclient.utils.player.MovementUtils.isMoving
import net.bloom.bloomclient.utils.player.PlayerUtils
import net.lenni0451.lambdaevents.EventHandler
import net.minecraft.network.play.client.C08PacketPlayerBlockPlacement
import net.minecraft.potion.Potion
import net.minecraft.util.BlockPos
import kotlin.math.floor

object AirTower : Mode("Air") {
    @EventHandler
    fun onUpdate(event: UpdateEvent) {
        if (mc.gameSettings.keyBindJump.isKeyDown && mc.thePlayer.ticksExisted % 2 == 0 && PlayerUtils.isPlayerNearBlockInRange(2)) {
            mc.thePlayer.motionY = 0.42
            mc.thePlayer.onGround = true
        }
    }
}

object LegitTower : Mode("Legit") {
    @EventHandler
    fun onUpdate(event: UpdateEvent) {
        if (mc.thePlayer.onGround && mc.gameSettings.keyBindJump.isKeyDown) {
            mc.thePlayer.jump()
        }
    }
}

object MatrixTower : Mode("Matrix") {
    @EventHandler
    fun onUpdate(event: UpdateEvent) {
        if (mc.gameSettings.keyBindJump.isKeyDown && PlayerUtils.isBlockUnder && mc.thePlayer.motionY < 0.2) {
            mc.thePlayer.motionY = 0.42
            mc.thePlayer.onGround = true
        }
    }
}

object NCPTower : Mode("NCP") {
    @EventHandler
    fun onUpdate(event: UpdateEvent) {
        if (mc.gameSettings.keyBindJump.isKeyDown && PlayerUtils.isPlayerNearBlockInRange(2)) {
            PacketComponent.sendPacketNoEvent(C08PacketPlayerBlockPlacement(null))

            if (mc.thePlayer.posY % 1 <= 0.00153598) {
                mc.thePlayer.setPosition(mc.thePlayer.posX, floor(mc.thePlayer.posY), mc.thePlayer.posZ)
                mc.thePlayer.motionY = 0.42
            } else if (mc.thePlayer.posY % 1 < 0.1 && mc.thePlayer.offGroundTicks != 0) {
                mc.thePlayer.motionY = 0.0
                mc.thePlayer.setPosition(mc.thePlayer.posX, floor(mc.thePlayer.posY), mc.thePlayer.posZ)
            }
        }
    }
}

object VanillaTower : Mode("Vanilla") {
    @EventHandler
    fun onUpdate(event: UpdateEvent) {
        if (mc.gameSettings.keyBindJump.isKeyDown && PlayerUtils.isPlayerNearBlockInRange(2)) {
            mc.thePlayer.motionY = 0.42
        }
    }
}

object VulcanTower : Mode("Vulcan") {
    @EventHandler
    fun onUpdate(event: UpdateEvent) {
        if (mc.gameSettings.keyBindJump.isKeyDown && PlayerUtils.isPlayerNearBlockInRange(2) && mc.thePlayer.offGroundTicks > 3) {
            val itemStack = mc.thePlayer.inventory.mainInventory[mc.thePlayer.inventory.currentItem]

            if (itemStack == null || (itemStack.stackSize > 2)) {
                PacketComponent.sendPacketNoEvent(C08PacketPlayerBlockPlacement(null))
            }
            mc.thePlayer.motionY = 0.42
        }
    }
}

object MMCTower : Mode("MMC") {
    @EventHandler
    fun onPacket(event: SentPacketEvent){
        val packet = event.packet

        if (mc.gameSettings.keyBindJump.isKeyDown && packet is C08PacketPlayerBlockPlacement) {
            if (packet.position == BlockPos(mc.thePlayer.posX, mc.thePlayer.posY - 1.4, mc.thePlayer.posZ)) {
                mc.gameSettings.keyBindSprint.pressed = false
                mc.thePlayer.isSprinting = false
                mc.thePlayer.motionY = 0.42
            }
        }
    }
}


object WatchDogTower : Mode("WatchDog") {
    @EventHandler
    fun onUpdate(event: UpdateEvent) {
        if (!mc.gameSettings.keyBindJump.isKeyDown || !isMoving) {
            return
        }

        if (mc.thePlayer.onGround) {
            mc.thePlayer.motionY = MovementUtils.getJumpBoostModifier(0.42F)
            mc.thePlayer.motionX *= .65
            mc.thePlayer.motionZ *= .65
        }
    }

    @EventHandler
    fun onPacket(event: SentPacketEvent){
        val packet = event.packet

        if (mc.thePlayer.motionY > -0.0784000015258789 && packet is C08PacketPlayerBlockPlacement) {
            if (packet.position == BlockPos(mc.thePlayer.posX, mc.thePlayer.posY - 1.4, mc.thePlayer.posZ)) {
                mc.thePlayer.motionY = -0.0784000015258789
            }
        }
    }
}


object NormalTower : Mode("Normal") {
    @EventHandler
    fun onUpdate(event: UpdateEvent) {
        if (mc.thePlayer.onGround && mc.gameSettings.keyBindJump.isKeyDown)
            mc.thePlayer.jump()
    }

    @EventHandler
    fun onPacket(event: SentPacketEvent){
        val packet = event.packet

        if (mc.thePlayer.motionY > -0.0784000015258789 && packet is C08PacketPlayerBlockPlacement) {
            if (packet.position == BlockPos(mc.thePlayer.posX, mc.thePlayer.posY - 1.4, mc.thePlayer.posZ)) {
                mc.thePlayer.motionY = -0.0784000015258789
            }
        }
    }
}

object LowHopTower: Mode("LowHop"){
    @EventHandler
    fun onUpdate(event: UpdateEvent){
        if(MovementUtils.isMoving)
            return

        if(mc.thePlayer.onGround && mc.gameSettings.keyBindJump.isKeyDown)
            mc.thePlayer.motionY = 0.37
    }
}

object FastJumpTower: Mode("FastJump"){
    @EventHandler
    fun onUpdate(event: UpdateEvent){
        if(mc.thePlayer.motionY < 0 && mc.gameSettings.keyBindJump.isKeyDown) {
            mc.thePlayer.motionY = 0.42

            if (mc.thePlayer.isPotionActive(Potion.jump)) {
                mc.thePlayer.motionY += (mc.thePlayer.getActivePotionEffect(Potion.jump).amplifier + 1) * 0.1f
            }
        }
    }
}

object NoTower : Mode("None")
