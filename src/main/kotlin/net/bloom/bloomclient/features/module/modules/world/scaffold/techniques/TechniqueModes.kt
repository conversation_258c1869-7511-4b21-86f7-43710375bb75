package net.bloom.bloomclient.features.module.modules.world.scaffold.techniques

import net.bloom.bloomclient.event.MoveInputEvent
import net.bloom.bloomclient.features.mode.Mode
import net.bloom.bloomclient.features.module.modules.world.ModuleScaffold
import net.bloom.bloomclient.features.module.modules.world.ModuleScaffold.targetPitch
import net.bloom.bloomclient.features.module.modules.world.ModuleScaffold.targetYaw
import net.bloom.bloomclient.features.noise.FastNoiseLite
import net.bloom.bloomclient.utils.extension.step
import net.bloom.bloomclient.utils.player.MovementUtils
import net.bloom.bloomclient.utils.player.MovementUtils.isMoving
import net.bloom.bloomclient.utils.player.RotationUtils
import net.bloom.bloomclient.utils.simulation.SimulatedPlayer
import net.bloom.bloomclient.utils.struct.MSTimer
import net.bloom.bloomclient.value.values.BoolValue
import net.bloom.bloomclient.value.values.IntegerValue
import net.lenni0451.lambdaevents.EventHandler
import net.minecraft.util.*
import kotlin.collections.iterator
import kotlin.math.round

open class TechniqueMode(name: String) : Mode(name) {
    open fun calculateRotation(movingObjectPos: MovingObjectPosition, blockPos: BlockPos, enumFacing: EnumFacing) {}
}

object NormalTechnique : TechniqueMode("Normal") {
    override fun calculateRotation(movingObjectPos: MovingObjectPosition, blockPos: BlockPos, enumFacing: EnumFacing) {
        if (movingObjectPos.blockPos != blockPos || movingObjectPos.sideHit != enumFacing)
            ModuleScaffold.calculateRotationByAim(blockPos, enumFacing)
    }
}

object ExpandTechnique : TechniqueMode("Expand") {
    val expand = IntegerValue("ExpandLength", 5, 0, 10)

    override fun calculateRotation(movingObjectPos: MovingObjectPosition, blockPos: BlockPos, enumFacing: EnumFacing) {
        mc.thePlayer ?: return

        if (movingObjectPos.blockPos == blockPos)
            return

        val rotationList = mutableListOf<Rotation>()
        val yaw = MathHelper.wrapAngleTo180(mc.thePlayer.playerYaw)

        for (pitch in 45.0..90.0 step 0.1) {
            val rotation = Rotation(yaw, pitch.toFloat())

            val hitBlock = mc.thePlayer.rayTrace(
                rotation.yaw,
                rotation.pitch,
                mc.playerController.blockReachDistance.toDouble(),
                1.0F
            )

            if (hitBlock.blockPos == blockPos)
                rotationList.add(rotation)
        }

        rotationList.minByOrNull {
            RotationUtils.getRotationDifference(it, mc.thePlayer.rotation)
        }?.let {
            targetYaw = it.yaw
            targetPitch = it.pitch
        }
    }
}

object NoRotationTechnique : TechniqueMode("NoRotation") {
    override fun calculateRotation(movingObjectPos: MovingObjectPosition, blockPos: BlockPos, enumFacing: EnumFacing) {
        targetYaw = mc.thePlayer.playerYaw
        targetPitch = mc.thePlayer.playerPitch
    }
}

object TellyTechnique : TechniqueMode("Telly") {
    private val ticks = IntegerValue("Ticks", 3, 0, 10)

    override fun calculateRotation(movingObjectPos: MovingObjectPosition, blockPos: BlockPos, enumFacing: EnumFacing) {
        if (mc.thePlayer.offGroundTicks >= ticks.get()) {
            if (movingObjectPos.blockPos != blockPos || movingObjectPos.sideHit != enumFacing)
                ModuleScaffold.calculateRotationByAim(blockPos, enumFacing)
        } else if (isMoving) {
            targetYaw = MathHelper.wrapAngleTo180(mc.thePlayer.playerYaw)
        }
    }
}

object WalkTechnique : TechniqueMode("Walk") {

    private val waitForRotation = BoolValue("WaitForRotation", false)
    private val startTimer = IntegerValue("StartSneakDelay", 250, 0, 1000) { waitForRotation.get() }

    private var timer = MSTimer()
    private var lastState = false

    override fun onEnable() {
        timer.reset()
    }

    @EventHandler
    fun onMoveInput(event: MoveInputEvent) {
        mc.thePlayer ?: return

        if (waitForRotation.get()) {
            if (RotationUtils.getRotationDifference(serverRotation) > 0)
                timer.reset()

            if (!timer.hasTimePassed(startTimer.get()))
                event.sneak = true
        }
    }

    override fun calculateRotation(movingObjectPos: MovingObjectPosition, blockPos: BlockPos, enumFacing: EnumFacing) {
        val direction = Math.toDegrees(MovementUtils.getDirection(mc.thePlayer.playerYaw)) + 180
        val movingYaw = round(direction / 45) * 45
        val isMovingForward = movingYaw % 90 == 0.0

        val player = SimulatedPlayer.fromClientPlayer(mc.thePlayer.movementInput, false)
        player.tick()

        if (!player.onGround && (movingObjectPos.sideHit != enumFacing || movingObjectPos.blockPos != blockPos || isMovingForward != lastState)) {
            ModuleScaffold.calculateRotationByAim(blockPos, enumFacing)
        }

        lastState = isMovingForward
    }

}

object GodBridgeTechnique : TechniqueMode("GodBridge") {
    private val waitForRotation = BoolValue("WaitForRotation", false)
    private val startTimer = IntegerValue("StartSneakDelay", 250, 0, 1000) { waitForRotation.get() }

    private var timer = MSTimer()
    private var lastState = false

    override fun onEnable() {
        timer.reset()
    }

    @EventHandler
    fun onMoveInput(event: MoveInputEvent) {
        mc.thePlayer ?: return

        if (waitForRotation.get()) {
            if (RotationUtils.getRotationDifference(serverRotation) > 0.5)
                timer.reset()

            if (!timer.hasTimePassed(startTimer.get()))
                event.sneak = true
        }
    }

    override fun calculateRotation(movingObjectPos: MovingObjectPosition, blockPos: BlockPos, enumFacing: EnumFacing) {
        val direction = Math.toDegrees(MovementUtils.getDirection(mc.thePlayer.playerYaw)) + 180
        val movingYaw = round(direction / 45) * 45
        val isMovingForward = movingYaw % 90 == 0.0
        val pitch = if (isMovingForward) 73.5f else 75.6f
        
        val blockCheck = if(movingObjectPos.sideHit != enumFacing) movingObjectPos.blockPos != blockPos else false

        if (blockCheck || isMovingForward != lastState) {
            for (yaw in floatArrayOf(-135f, -45f, 45f, 135f)) {
                val rotation = Rotation(yaw, pitch)

                val hitBlock = mc.thePlayer.rayTrace(
                    rotation.yaw,
                    rotation.pitch,
                    mc.playerController.blockReachDistance.toDouble(),
                    1.0F
                )

                if (hitBlock.blockPos == blockPos) {
                    targetYaw = rotation.yaw
                    targetPitch = rotation.pitch
                }
            }
        }

        lastState = isMovingForward
    }
}

object PitchMultiplyTechnique : TechniqueMode("PitchMultiply") {
    private var randomizeTicks = 0
    private var noise = FastNoiseLite()

    override fun calculateRotation(movingObjectPos: MovingObjectPosition, blockPos: BlockPos, enumFacing: EnumFacing) {
        var pitchMultiplier = 8
        var pitchAdd: Float

        targetYaw = MathHelper.wrapAngleTo180(mc.thePlayer.playerYaw) - 180F

        if (!isMoving)
            return

        if (movingObjectPos.blockPos != blockPos) {
            for (pitchSpeed in 0..720) {
                if (pitchSpeed >= 100 && mc.objectMouseOver.sideHit != EnumFacing.UP)
                    pitchMultiplier = 12

                pitchAdd = noise.GetNoise(randomizeTicks * pitchSpeed.toFloat(), randomizeTicks * pitchSpeed.toFloat()) * pitchMultiplier.toFloat()

                val pitch = (targetPitch + pitchAdd).coerceIn(-90f..90f)
                val rotation = Rotation(targetYaw, pitch)

                if (ModuleScaffold.isObjectMouseOverBlock(enumFacing, blockPos, rotation) != null) {
                    targetPitch = pitch
                    break
                }

                ++randomizeTicks
            }
        } else {
            for (pitchSpeed in 0..100) {
                pitchAdd = noise.GetNoise(randomizeTicks * pitchSpeed.toFloat(), randomizeTicks * pitchSpeed.toFloat()) * pitchMultiplier.toFloat()

                val pitch = (targetPitch + pitchAdd).coerceIn(-90f..90f)
                val rotation = Rotation(targetYaw, pitch)

                if (ModuleScaffold.isObjectMouseOverBlock(enumFacing, blockPos, rotation) != null)
                    targetPitch = pitch

                ++randomizeTicks
            }
        }
    }
}