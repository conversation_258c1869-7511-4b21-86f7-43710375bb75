package net.bloom.bloomclient.features.module.modules.combat

import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.value.values.BoolValue
import net.minecraft.entity.player.EntityPlayer

object ModuleAntiBot : <PERSON><PERSON><PERSON>(name = "AntiBot", description = "", category = ModuleCategory.COMBAT) {
    private val noArmor by <PERSON><PERSON><PERSON><PERSON><PERSON>("NoArmor", true)

    fun isBot(player: EntityPlayer): <PERSON><PERSON>an {
        if (!state)
            return false

        if (noArmor) {
            if ((3 downTo 0 step 1).all { player.inventory.armorInventory[it] == null })
                return true
        }

        return false
    }
}