package net.bloom.bloomclient.features.module.modules.combat.killaura.randomizedrotation

import net.bloom.bloomclient.features.mode.Mode
import net.bloom.bloomclient.features.noise.OpenSimplex2S
import net.bloom.bloomclient.utils.RandomUtils
import net.bloom.bloomclient.utils.player.RotationUtils
import net.bloom.bloomclient.value.values.FloatRangeValue
import net.bloom.bloomclient.value.values.FloatValue
import net.minecraft.util.VectorRotation
import kotlin.math.cos
import kotlin.math.max
import kotlin.math.min
import kotlin.random.Random

abstract class RandomizedRotationMode(mode: String): Mode(mode) {
    abstract fun randomize(vectorRotation: VectorRotation): VectorRotation
}

object NoRandomizedRotationMode: RandomizedRotationMode("None") {
    override fun randomize(vectorRotation: VectorRotation) = vectorRotation
}

object NoiseRandomizedRotationMode: RandomizedRotationMode("Noise") {
    private val randomSpeed = FloatRangeValue("RandomSpeed", 1f, 3f, 0f, 180f)
    private val limit = FloatRangeValue("Limit", 1f, 5f, 0f, 180f)
    private val chance = FloatValue("Chance", 0.5f, 0f, 1f)

    override fun randomize(vectorRotation: VectorRotation): VectorRotation {
        val randomizationSpeed = RandomUtils.nextFloat(randomSpeed.minValue, randomSpeed.maxValue)
        val position = (mc.thePlayer.ticksExisted * randomizationSpeed).toDouble()

        val noiseX1 = position
        val noiseY1 = position + 50
        val noiseZ1 = position + 100
        val time = System.currentTimeMillis() / 10000.0

        val noiseX2 = position + 150
        val noiseY2 = position + 200
        val noiseZ2 = position + 250

        //Noise apply
        var noiseYaw = vectorRotation.rotation.yaw + 2f
        var noisePitch = vectorRotation.rotation.pitch + 2f

        if(Random(1337).nextDouble() > chance.get()){
            noiseYaw += (OpenSimplex2S.noise4_ImproveXYZ_ImproveXZ(Random(1337).nextLong(), noiseX1, noiseY1, noiseZ1, time) * randomizationSpeed) * max(1.0f, min(3.0f, mc.thePlayer.getDistanceToEntity(vectorRotation.entity)) / 3.0f)
            noisePitch += + (OpenSimplex2S.noise4_ImproveXYZ_ImproveXZ(Random(1337).nextLong(), noiseX2, noiseY2, noiseZ2, time) * randomizationSpeed) * max(1.0f, min(3.0f, mc.thePlayer.getDistanceToEntity(vectorRotation.entity)) / 3.0f)
        }

        //Smooth apply
        val currentYaw = vectorRotation.rotation.yaw
        val currentPitch = vectorRotation.rotation.pitch

        val min = limit.minValue
        val max = limit.maxValue

        var diffAngleYaw = RotationUtils.getAngleDifference(currentYaw, noiseYaw).coerceAtMost(max)
        var diffAnglePitch = RotationUtils.getAngleDifference(currentPitch, noisePitch).coerceAtMost(max)

        val angleFractionYaw = diffAngleYaw / 2f
        val angleFractionPitch = diffAnglePitch / 2f

        val cosFractionYaw = (-cos(angleFractionYaw * Math.PI).toFloat() * 0.5f + 0.5f)
        val cosFractionPitch = (-cos(angleFractionPitch * Math.PI).toFloat() * 0.5f + 0.5f)

        val quadSineEaseYaw = cosFractionYaw * cosFractionYaw
        val quadSineEasePitch = cosFractionPitch * cosFractionPitch

        val finalYaw = quadSineEaseYaw * max + (1 - quadSineEaseYaw) * min
        val finalPitch = quadSineEasePitch * max + (1 - quadSineEasePitch) * min

        //Apply
        vectorRotation.rotation.yaw += finalYaw
        vectorRotation.rotation.pitch += finalPitch

        return vectorRotation
    }

}