package net.bloom.bloomclient.features.module.modules.combat

import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.features.module.modules.combat.velocity.*
import net.bloom.bloomclient.value.values.ModeValue

object ModuleVelocity : Module(name = "Velocity", description = "Reduce or anti knockback.", category = ModuleCategory.COMBAT) {

    val modes = ModeValue("Mode", arrayOf(
        SimpleVelocityMode,
        CancelVelocityMode,
        AttackReduceVelocityMode,
        LegitVelocityMode,
        JumpVelocityMode
    ), this)

    override fun onEnable() {
        modes.get().onEnable()
    }

    override fun onDisable() {
        modes.get().onDisable()
    }

}
