package net.bloom.bloomclient.utils

import net.bloom.bloomclient.BloomClient
import net.bloom.bloomclient.file.FileManager
import java.io.File
import java.util.*
import kotlin.random.Random

object DictUtils {
    private var dict: MutableList<String>? = null

    fun init() {
        val dictFile = File(FileManager.dir, "dict.txt")
        if (!dictFile.exists()) {
            this::class.java.getResourceAsStream("/assets/minecraft/bloomclient/dict.txt")!!.buffered().use {
                File(FileManager.dir, "dict.txt").writeBytes(it.readAllBytes())
            }
            ClientUtils.LOGGER.info("[DictUtils] Extracted dictionary")
        }

        dict = mutableListOf()
        dict!!.addAll(dictFile.readText().lines().filter { !it.contains(Regex("\\s")) })
        ClientUtils.LOGGER.info("[DictUtils] Loaded ${dict!!.size} words from dictionary")

    }

    private fun getInternal(format: String): String {
        var name = format
        name = name
            .replace(Regex("%w")) { dict!!.random() }
            .replace(Regex("%W")) { dict!!.random().replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() } }
            .replace(Regex("%d")) { Random.nextInt(10).toString() }
            .replace(Regex("%c")) { "abcdefghijklmnopqrstuvwxyz".random().toString() }
            .replace(Regex("%C")) { "ABCDEFGHIJKLMNOPQRSTUVWXYZ".random().toString() }

        return name
    }

    fun get(format: String): String {
        var s: String
        do {
            s = getInternal(format)
        } while (s.length > 16)

        return s
    }
}