package net.bloom.bloomclient.utils

import com.google.gson.JsonObject
import net.bloom.bloomclient.BloomClient
import net.minecraft.client.MinecraftInstance
import net.minecraft.util.IChatComponent
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger


object ClientUtils: MinecraftInstance() {

    val LOGGER: Logger = LogManager.getLogger("BloomClient")

    fun displayChatMessage(message: String) {
        mc.thePlayer ?: run {
            LOGGER.info("(Minecraft Chat) $message")
            return
        }

        val jsonObject = JsonObject()
        jsonObject.addProperty("text", message)
        mc.thePlayer.addChatMessage(IChatComponent.Serializer.jsonToComponent(jsonObject.toString()))
    }

    fun displayChatMessage(message: Any) = displayChatMessage(message.toString())

    fun displaySystemMessage(message: Any) = displayChatMessage("[${BloomClient.CLIENT_NAME}] $message")

}