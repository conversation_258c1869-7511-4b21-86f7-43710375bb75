package net.bloom.bloomclient.utils.io

import net.bloom.bloomclient.utils.StringUtils
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.security.MessageDigest

/**
 * A hash utils for check hash of file
 * <AUTHOR>
 */

object HashUtils {

    private const val STREAM_BUFFER_LENGTH = 1024

    fun isTwoFilesSameAs(file1: File, file2: File): Boolean {
        val hash1 = getCheckSumFromFile("SHA-1", file1)
        val hash2 = getCheckSumFromFile("SHA-1", file2)

        return hash1 == hash2
    }

    fun isFileMatchHash(file: File, hash: String, type: String = MessageDigestAlgorithm.SHA_1): Boolean {
        if (!file.isFile)
            return false

        val hashFromFile = getCheckSumFromFile(MessageDigest.getInstance(type), file)

        return hashFromFile == hash
    }

    fun getCheckSumFromFile(digest: String, file: File) = getCheckSumFromFile(MessageDigest.getInstance(digest), file)

    private fun getCheckSumFromFile(digest: MessageDigest, file: File): String {
        val fis = FileInputStream(file)
        val byteArray = updateDigest(digest, fis).digest()
        fis.close()
        val hexCode = StringUtils.encodeHex(byteArray, true)
        return String(hexCode)
    }

    /**
     * Reads through an InputStream and updates the digest for the data
     *
     * @param digest The MessageDigest to use (e.g. MD5)
     * @param data Data to digest
     * @return the digest
     */
    private fun updateDigest(digest: MessageDigest, data: InputStream): MessageDigest {
        val buffer = ByteArray(STREAM_BUFFER_LENGTH)
        var read = data.read(buffer, 0, STREAM_BUFFER_LENGTH)
        while (read > -1) {
            digest.update(buffer, 0, read)
            read = data.read(buffer, 0, STREAM_BUFFER_LENGTH)
        }
        return digest
    }

}

object MessageDigestAlgorithm {
    const val MD5 = "MD5"
    const val SHA_1 = "SHA-1"
}