package net.bloom.bloomclient.utils

import net.bloom.bloomclient.utils.MathUtils.lerp
import net.bloom.bloomclient.utils.render.NanoVGUtils.roundedRect
import net.bloom.bloomclient.utils.struct.MSTimer
import org.lwjgl.input.Mouse
import java.awt.Color

class ScrollUtil {
    var max = 0F
    var scroll = 0F
    private var target = 0F
    private val stretch = 30F
    private val scrollTiming = MSTimer()
    private val animationTiming = MSTimer()

    fun update() {
        if (scrollTiming.hasTimePassed(70)) {
            val wheel = -Mouse.getDWheel() * 30F

            target = if (wheel != 0F)
                    (target + wheel).coerceIn(-stretch, max + stretch)
            else
                target.coerceIn(0F, max)

            scrollTiming.reset()
        }

        val deltaTime = animationTiming.reachedTime
        for (i in 0..<deltaTime) {
            scroll = lerp(scroll, target, 1E-2F)
        }
        animationTiming.reset()
    }

    fun renderScrollBar(x: Float, y: Float, height: Float) {
        val percentage = scroll / max
        val scrollBarHeight = height - ((max / (max - height)) * height)

        if (scrollBarHeight > height) return

        val scrollY = y + height * percentage - scrollBarHeight * percentage
        roundedRect(x, scrollY, 3, scrollBarHeight, 2, Color(255, 255, 255, 60))
    }

    fun reset() {
        scroll = 0F
        target = 0F
        scrollTiming.reset()
        animationTiming.reset()
    }
}