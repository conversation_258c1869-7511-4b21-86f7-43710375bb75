package net.bloom.bloomclient.utils.render.shader.shaders.font

import net.bloom.bloomclient.utils.ClientUtils
import net.bloom.bloomclient.utils.render.shader.Shader
import org.lwjgl.opengl.GL20.*
import java.io.Closeable

object GradientFontShader : Shader("gradient_font.frag"), Closeable {
    var isInUse = false
        private set

    var strengthX = 0f
    var strengthY = 0f
    var offset = 0f
    var speed = 1f

    var maxColors = 9
    var colors = Array(maxColors) { floatArrayOf(0f, 0f, 0f, 1f) }

    override fun setupUniforms() {
        setupUniform("offset")
        setupUniform("strength")
        setupUniform("speed")
        setupUniform("maxColors")

        for (i in 0 until 9) {
            try {
                setupUniform("colors[$i]")
            } catch (e: Exception) {
                ClientUtils.LOGGER.error("${javaClass.name} setup uniforms error.", e)
            }
        }
    }

    override fun updateUniforms() {
        glUniform2f(getUniform("strength"), strengthX, strengthY)
        glUniform1f(getUniform("offset"), offset)
        glUniform1f(getUniform("speed"), speed)
        glUniform1i(getUniform("maxColors"), maxColors)

        for (i in 0 until maxColors) {
            try {
                glUniform4f(getUniform("colors[$i]"), colors[i][0], colors[i][1], colors[i][2], colors[i][3])
            } catch (e: Exception) {
                ClientUtils.LOGGER.error("${javaClass.name} update uniforms error.", e)
            }
        }
    }

    override fun startShader() {
        super.startShader()
        isInUse = true
    }

    override fun stopShader() {
        super.stopShader()
        isInUse = false
    }

    override fun close() {
        if (isInUse)
            stopShader()
    }

    fun begin(enable: Boolean, x: Float, y: Float, gradient: List<FloatArray>, speed: Float, offset: Float): GradientFontShader {
        if (enable) {
            strengthX = x
            strengthY = y
            maxColors = gradient.size
            colors = gradient.toTypedArray()
            GradientFontShader.speed = speed
            GradientFontShader.offset = offset

            startShader()
        }

        return this
    }
}