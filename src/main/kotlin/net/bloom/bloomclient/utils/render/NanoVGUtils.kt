package net.bloom.bloomclient.utils.render

import net.minecraft.client.MinecraftInstance
import net.minecraft.client.gui.ScaledResolution
import net.minecraft.client.gui.Side
import net.minecraft.client.renderer.GlStateManager.*
import org.lwjgl.nanovg.NVGColor
import org.lwjgl.nanovg.NanoVG.*
import org.lwjgl.nanovg.NanoVGGL3
import org.lwjgl.opengl.Display
import org.lwjgl.opengl.GL11.*
import org.lwjgl.opengl.GL13
import java.awt.Color

/**
 * NanoVG rendering utility
 *
 * <AUTHOR> KhoiBruh, Blend Client
 */
object NanoVGUtils : MinecraftInstance() {
    // We should use GL3 as it improve performance
    val context = NanoVGGL3.nvgCreate(NanoVGGL3.NVG_ANTIALIAS or NanoVGGL3.NVG_STENCIL_STROKES)

    /**
     * Setup rendering for NanoVG
     */
    fun setup() {
        glPushAttrib(GL_ALL_ATTRIB_BITS)
        pushMatrix()
        disableAlpha()
        nvgBeginFrame(context, Display.getWidth().toFloat(), Display.getHeight().toFloat(), 1.0f)
        val sr = ScaledResolution(mc)
        save()
        scale(sr.scaleFactor)
    }

    /**
     * Close NanoVG rendering
     */
    fun close() {
        restore()
        nvgEndFrame(context)
        enableAlpha()
        glPopAttrib()
        popMatrix()
    }

    /**
     * Save current render state
     */
    fun save() {
        nvgSave(context)
    }

    /**
     * Restore current render state
     */
    fun restore() {
        nvgRestore(context)
    }

    /**
     * Scale things to the given scale
     *
     * @param scale Scale value to scale
     */
    fun scale(scale: Number) {
        scale(scale, scale)
    }

    /**
     * Scale things to the given x and y scales
     *
     * @param x X scale to scale
     * @param y Y scale to scale
     */
    fun scale(x: Number, y: Number) {
        nvgScale(context, x.toFloat(), y.toFloat())
    }

    /**
     * Translate things to the given position
     *
     * @param x X position to translate to
     * @param y Y position to translate to
     */
    fun translate(x: Number, y: Number) {
        nvgTranslate(context, x.toFloat(), y.toFloat())
    }

    fun bindTexture(texture: Int) {
        GL13.glActiveTexture(GL13.GL_TEXTURE0)
        glBindTexture(GL_TEXTURE_2D, texture)
    }

    /**
     * Draw a box that only render things inside it
     */
    fun scissor(x: Number, y: Number, width: Number, height: Number, renderFunc: () -> Unit) {
        nvgScissor(context, x.toFloat(), y.toFloat(), width.toFloat(), height.toFloat())
        renderFunc()
        nvgResetScissor(context)
    }

    /**
     * Draws a filled circle at the specified position with the given radius and color.
     *
     * @param centerX X coordinate of the circle's center.
     * @param centerY Y coordinate of the circle's center.
     * @param radius Radius of the circle.
     * @param color Color of the circle.
     *
     * <AUTHOR>
     */
    fun circle(centerX: Number, centerY: Number, radius: Number, color: Color) {
        nvgBeginPath(context)
        nvgCircle(context, centerX.toFloat(), centerY.toFloat(), radius.toFloat())
        nvgFillColor(context, color.toNVGColor())
        nvgFill(context)
    }

    /**
     * Draw a filled rectangle at the specified position with the given size and color
     *
     * @param x X position of the rectangle
     * @param y Y position of the rectangle
     * @param width Width of the rectangle
     * @param height Height of the rectangle
     * @param color Color od the rectangle
     *
     * <AUTHOR>
     */
    fun rect(x: Number, y: Number, width: Number, height: Number, color: Color) {
        nvgBeginPath(context)
        nvgRect(context, x.toFloat(), y.toFloat(), width.toFloat(), height.toFloat())
        nvgFillColor(context, color.toNVGColor())
        nvgFill(context)
    }

    /**
     * Draw a filled rounded rectangle at the specified position with the given size, rounding radius and color
     *
     * @param x X position of the rectangle
     * @param y Y position of the rectangle
     * @param width Width of the rectangle
     * @param height Height of the rectangle
     * @param radius Rounding radius of the rectangle
     * @param color Color od the rectangle
     * @param topLeft Top left rounding
     * @param topRight Top right rounding
     * @param bottomRight Bottom Right rounding
     * @param bottomLeft Bottom left rounding
     *
     * <AUTHOR>
     */
    fun roundedRect(
        x: Number, y: Number, width: Number, height: Number,
        radius: Number, color: Color, topLeft: Boolean = true,
        topRight: Boolean = true, bottomRight: Boolean = true,
        bottomLeft: Boolean = true
    ) {
        val radTopLeft = if (topLeft) radius.toFloat() else 0F
        val radTopRight = if (topRight) radius.toFloat() else 0F
        val radBottomRight = if (bottomRight) radius.toFloat() else 0F
        val radBottomLeft = if (bottomLeft) radius.toFloat() else 0F

        nvgBeginPath(context)
        nvgRoundedRectVarying(
            context, x.toFloat(), y.toFloat(), width.toFloat(), height.toFloat(),
            radTopLeft, radTopRight, radBottomLeft, radBottomRight
        )
        nvgFillColor(context, color.toNVGColor())
        nvgFill(context)
    }

    /**
     * Draw a filled rounded outline rectangle at the specified position with the given size, rounding radius and color
     *
     * @param x X position of the rectangle
     * @param y Y position of the rectangle
     * @param width Width of the rectangle
     * @param height Height of the rectangle
     * @param radius Rounding radius of the rectangle
     * @param stroke Outline size
     * @param color Color of the rectangle
     * @param strokeColor Stroke color
     * @param topLeft Top left rounding
     * @param topRight Top right rounding
     * @param bottomRight Bottom Right rounding
     * @param bottomLeft Bottom left rounding
     *
     * <AUTHOR>
     */
    fun roundedOutlineRect(
        x: Number, y: Number, width: Number, height: Number,
        radius: Number, stroke: Number, color: Color, strokeColor: Color,
        topLeft: Boolean = true, topRight: Boolean = true,
        bottomRight: Boolean = true, bottomLeft: Boolean = true
    ) {
        val radTopLeft = if (topLeft) radius.toFloat() else 0F
        val radTopRight = if (topRight) radius.toFloat() else 0F
        val radBottomRight = if (bottomRight) radius.toFloat() else 0F
        val radBottomLeft = if (bottomLeft) radius.toFloat() else 0F

        nvgBeginPath(context)
        nvgRoundedRectVarying(
            context, x.toFloat(), y.toFloat(), width.toFloat(), height.toFloat(),
            radTopLeft, radTopRight, radBottomLeft, radBottomRight
        )

        nvgFillColor(context, color.toNVGColor())
        nvgFill(context)

        nvgStrokeWidth(context, stroke.toFloat())
        nvgStrokeColor(context, color.toNVGColor())
        nvgStroke(context)
    }

    @JvmOverloads
    fun rect(
        startXIn: Float, startYIn: Float, endXIn: Float, endYIn: Float,
        radius: Float = 0f, color: Color = Color.WHITE,
        roundTL: Boolean = false, roundTR: Boolean = false,
        roundBL: Boolean = false, roundBR: Boolean = false,
        border: Boolean = false, borderWidth: Float = 1f
    ) {
        val nvgColor = NVGColor.calloc()

        val radTL = if (roundTL) radius else 0f
        val radTR = if (roundTR) radius else 0f
        val radBR = if (roundBR) radius else 0f
        val radBL = if (roundBL) radius else 0f

        nvgBeginPath(context)
        nvgShapeAntiAlias(context, true)
        nvgRoundedRectVarying(context, startXIn, startYIn, endXIn, endYIn, radTL, radTR, radBR, radBL)
        nvgRGBAf(color.red / 255f, color.green / 255f, color.blue / 255f, color.alpha / 255f, nvgColor)

        if (border) {
            nvgStrokeWidth(context, borderWidth)
            nvgStrokeColor(context, nvgColor)
            nvgStroke(context)
        } else {
            nvgFillColor(context, nvgColor)
            nvgFill(context)
        }

        nvgClosePath(context)

        nvgColor.close()
    }

    /**
     * @author: longathelstan
     */
    fun drawShadowRoundedRect(
        x: Float, y: Float,
        width: Float, height: Float,
        radius: Float, shadowSize: Float,
        shadowAlpha: Float, color: Color
    ) {
        for (i in 1..5) {
            val alpha = (shadowAlpha * (1f - i / 6f) * 255).toInt()
            val shadowColor = Color(0, 0, 0, alpha)
            val offset = shadowSize * i / 5f

            rect(
                x - offset, y - offset,
                width + offset * 2, height + offset * 2,
                radius + offset / 2f, shadowColor
            )
        }

        rect(x, y, width, height, radius, color)
    }

    fun drawShadowOutlineRoundedRect(
        x: Float, y: Float,
        width: Float, height: Float,
        stroke: Float, radius: Float,
        shadowSize: Float, shadowAlpha: Float,
        fillColor: Color, borderColor: Color
    ) {
        for (i in 1..5) {
            val alpha = (shadowAlpha * (1f - i / 6f) * 255).toInt()
            val shadowColor = Color(0, 0, 0, alpha)
            val offset = shadowSize * i / 5f

            rect(
                x - offset, y - offset,
                width + offset * 2, height + offset * 2,
                radius + offset / 2f, shadowColor
            )
        }

        roundedOutlineRect(x, y, width, height, stroke, radius, fillColor, borderColor)
    }

    fun getNanoVGSide(side: Side): Int {
        val horizontalAlign = when (side.horizontal) {
            Side.Horizontal.LEFT -> NVG_ALIGN_LEFT
            Side.Horizontal.MIDDLE -> NVG_ALIGN_CENTER
            Side.Horizontal.RIGHT -> NVG_ALIGN_RIGHT
        }

        val verticalAlign = when (side.vertical) {
            Side.Vertical.TOP -> NVG_ALIGN_TOP
            Side.Vertical.MIDDLE -> NVG_ALIGN_MIDDLE
            Side.Vertical.BOTTOM -> NVG_ALIGN_BOTTOM
        }

        return horizontalAlign or verticalAlign
    }

    fun drawNanoVG(drawFunction: () -> Unit) {
        setup()
        save()

        drawFunction()

        restore()
        close()
    }

    /**
     * Extension function for Color class to convert color to NVGColor
     *
     * <AUTHOR>
     */
    fun Color.toNVGColor(): NVGColor {
        val nvgColor = NVGColor.create()

        nvgColor.r(this.red / 255.0f)
        nvgColor.g(this.green / 255.0f)
        nvgColor.b(this.blue / 255.0f)
        nvgColor.a(this.alpha / 255.0f)

        return nvgColor
    }
}