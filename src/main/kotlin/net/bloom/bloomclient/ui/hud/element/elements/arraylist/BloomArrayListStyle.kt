package net.bloom.bloomclient.ui.hud.element.elements.arraylist

import net.bloom.bloomclient.BloomClient
import net.bloom.bloomclient.font.Fonts
import net.bloom.bloomclient.ui.hud.element.Border
import net.bloom.bloomclient.utils.render.NanoVGUtils
import net.minecraft.client.gui.Side
import java.awt.Color
import kotlin.math.max
import net.bloom.bloomclient.ui.hud.element.elements.ArrayList

class BloomArrayListStyle(parent: ArrayList): ArrayListStyle(parent, "Bloom") {

    private val lexendFont = Fonts.fontLexend[10]

    private val backgroundColor = Color(32, 34, 37, 220)
    private val textColor = Color(255, 255, 255)
    private val moduleSpacing = 2f
    private val rectRounding = 4f
    private val hoverPadding = 4f
    private val shadowRadius = 3f
    private val shadowAlpha = 0.2f

    override fun drawElement(): Border {
        val sortedModules = BloomClient.moduleManager.modules
            .filter { it.state || it.alphaAnimation.current > 0.01f } // Show if enabled or still fading
            .sortedByDescending { lexendFont.width(it.displayName) }

        if (sortedModules.isEmpty())
            return Border(0f, 0f, 0f, 0f)

        updateAnimations()

        NanoVGUtils.setup()
        NanoVGUtils.save()

        var yPos = parent.renderY.toFloat()
        var maxWidth = 0f
        var maxHeight = 0f

        // Draw each module with animations
        for ((index, module) in sortedModules.withIndex()) {
            if (module.alphaAnimation.current <= 0.01f)
                continue

            val textWidth = lexendFont.width(module.displayName)
            val rectWidth = textWidth + hoverPadding * 2
            val rectHeight = lexendFont.height.toFloat() + hoverPadding

            // Calculate position based on side settings and animation
            val xOffset = when (parent.side.horizontal) {
                Side.Horizontal.LEFT -> 0f
                Side.Horizontal.MIDDLE -> -rectWidth / 2
                Side.Horizontal.RIGHT -> -rectWidth
            }

            val xPos = parent.renderX.toFloat() + xOffset

            // Apply slide animation
            val animatedXPos = when (parent.side.horizontal) {
                Side.Horizontal.LEFT -> xPos - (1f - module.alphaAnimation.current) * rectWidth
                Side.Horizontal.MIDDLE -> xPos
                Side.Horizontal.RIGHT -> xPos + (1f - module.alphaAnimation.current) * rectWidth
            }

            val newAlpha = (backgroundColor.alpha * module.alphaAnimation.current).toInt()

            // Generate module color with animation
            val moduleColor = Color(backgroundColor.red, backgroundColor.green, backgroundColor.blue, newAlpha)

            when (index) {
                0 -> {
                    NanoVGUtils.rect(animatedXPos, yPos, rectWidth, rectHeight, rectRounding, moduleColor,
                        roundTL = true, roundTR = true, roundBL = false, roundBR = false
                    )
                }
                sortedModules.size - 1 -> {
                    NanoVGUtils.rect(animatedXPos, yPos, rectWidth, rectHeight, rectRounding, moduleColor,
                        roundTL = false, roundTR = false, roundBL = true, roundBR = true
                    )
                }
                else -> {
                    NanoVGUtils.rect(animatedXPos, yPos, rectWidth, rectHeight, rectRounding, moduleColor)
                }
            }

            // Calculate text position
            val textX = when (parent.side.horizontal) {
                Side.Horizontal.MIDDLE -> animatedXPos + (rectWidth - textWidth)
                else -> animatedXPos + hoverPadding
            }
            val textY = yPos + hoverPadding / 2

            // Draw text with animation
            val textAlpha = (255 * module.alphaAnimation.current).toInt()
            val moduleTextColor = Color(
                textColor.red,
                textColor.green,
                textColor.blue,
                textAlpha
            )

            lexendFont.string(
                module.displayName,
                textX,
                textY,
                moduleTextColor
            )

            yPos += rectHeight * module.alphaAnimation.current
            maxWidth = max(maxWidth, rectWidth)
            maxHeight += rectHeight * module.alphaAnimation.current
        }

        NanoVGUtils.restore()
        NanoVGUtils.close()

        // Calculate border based on alignment
        return when (parent.side.horizontal) {
            Side.Horizontal.LEFT -> Border(parent.renderX, parent.renderY, parent.renderX + maxWidth, parent.renderY + maxHeight)
            Side.Horizontal.MIDDLE -> Border(parent.renderX - maxWidth / 2, parent.renderY, parent.renderX + maxWidth / 2, parent.renderY + maxHeight)
            Side.Horizontal.RIGHT -> Border(parent.renderX - maxWidth, parent.renderY, parent.renderX, parent.renderY + maxHeight)
        }
    }

    /**
     * Update animation values for each module
     */
    private fun updateAnimations() {
        BloomClient.moduleManager.modules.forEach {
            it.alphaAnimation.target = if (it.state) 1f else 0f
            it.alphaAnimation.update(0.15f )
        }
    }
}