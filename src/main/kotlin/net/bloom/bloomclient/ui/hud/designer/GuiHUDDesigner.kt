package net.bloom.bloomclient.ui.hud.designer

import net.bloom.bloomclient.ui.hud.HUD
import net.bloom.bloomclient.ui.hud.designer.panel.CreatePanel
import net.bloom.bloomclient.ui.hud.designer.panel.DesignerPanel
import net.bloom.bloomclient.ui.hud.designer.panel.EditorPanel
import net.bloom.bloomclient.ui.hud.designer.panel.SelectionPanel
import net.minecraft.client.gui.GuiScreen
import org.lwjgl.input.Keyboard
import org.lwjgl.input.Mouse

object GuiHUDDesigner: GuiScreen() {
    var currentPanel: DesignerPanel = SelectionPanel(width / 2f, height / 2f)
    val selectionPanel = SelectionPanel(currentPanel.x, currentPanel.y)

    override fun initGui() {
        Keyboard.enableRepeatEvents(true)
    }

    override fun drawScreen(mouseX: Int, mouseY: Int, partialTicks: Float) {
        HUD.render(true)
        HUD.handleMouseMove(mouseX, mouseY)

        currentPanel.drag(mouseX, mouseY)
        currentPanel.drawPanel(mouseX, mouseY)

        val wheel = Mouse.getDWheel()
        if (wheel != 0) {
            for (element in HUD.elements) {
                if (element.isInBorder(mouseX / element.scale - element.renderX, mouseY / element.scale - element.renderY)) {
                    element.scale += if (wheel > 0) 0.05f else -0.05f
                    break
                }
            }
        }

        super.drawScreen(mouseX, mouseY, partialTicks)
    }

    override fun mouseClicked(mouseX: Int, mouseY: Int, mouseButton: Int) {
        super.mouseClicked(mouseX, mouseY, mouseButton)

        HUD.handleMouseClick(mouseX, mouseY, mouseButton)

        if (mouseButton != 0)
            return

        currentPanel.handleMouseClick(mouseX, mouseY, mouseButton)

        for (element in HUD.elements) {
            if (element.isInBorder(mouseX / element.scale - element.renderX, mouseY / element.scale - element.renderY)) {
                currentPanel = EditorPanel(element, currentPanel.x, currentPanel.y)
                break
            }
        }
    }

    override fun mouseReleased(mouseX: Int, mouseY: Int, state: Int) {
        super.mouseReleased(mouseX, mouseY, state)

        HUD.handleMouseReleased()
    }

    override fun onGuiClosed() {
        Keyboard.enableRepeatEvents(false)

        super.onGuiClosed()
    }

    override fun keyTyped(typedChar: Char, keyCode: Int) {
        val currentPanel = this.currentPanel

        when (keyCode) {
            Keyboard.KEY_DELETE -> {
                if (currentPanel is EditorPanel) {
                    this.currentPanel = selectionPanel.also {
                        it.x = currentPanel.x
                        it.y = currentPanel.y
                    }
                    HUD.removeElement(currentPanel.element)
                }
            }

            Keyboard.KEY_ESCAPE -> when (currentPanel) {
                is EditorPanel, is CreatePanel -> {
                    this.currentPanel = selectionPanel.also {
                        it.x = currentPanel.x
                        it.y = currentPanel.y
                    }
                    return
                }
            }

            else -> HUD.handleKey(typedChar, keyCode)
        }

        super.keyTyped(typedChar, keyCode)
    }

}