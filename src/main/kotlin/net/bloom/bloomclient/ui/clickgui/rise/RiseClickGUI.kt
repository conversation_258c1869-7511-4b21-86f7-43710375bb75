package net.bloom.bloomclient.ui.clickgui.rise

import net.bloom.bloomclient.BloomClient
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.features.module.modules.other.ModuleClickGUI
import net.bloom.bloomclient.font.Fonts
import net.bloom.bloomclient.ui.clickgui.rise.button.CategoryButton
import net.bloom.bloomclient.utils.MathUtils.mouseOver
import net.bloom.bloomclient.utils.animations.Animation
import net.bloom.bloomclient.utils.animations.Easing
import net.bloom.bloomclient.utils.render.NanoVGUtils.roundedRect
import net.bloom.bloomclient.utils.render.NanoVGUtils.scale
import net.bloom.bloomclient.utils.render.NanoVGUtils.scissor
import net.bloom.bloomclient.utils.render.NanoVGUtils.translate
import net.minecraft.client.gui.GuiScreen
import org.lwjgl.util.vector.Vector2f
import java.awt.Color

/**
 * Rise-like ClickGUI for Bloom client
 *
 * I think this is better
 * @author: KhoiBruh
 **/
object RiseClickGUI : GuiScreen() {

    // Animation things
    private val categoryAnimation = Animation(Easing.EASE_IN_OUT_EXPO, 300)
    private val scaleAnimation = Animation(Easing.EASE_IN_OUT_EXPO, 700)
    private var animatedScale = 0.0

    // Font things
    private val icon12Font = Fonts.fontIconGUI[32]
    private val titleFont = Fonts.fontNano[16]

    // position things
    val pos = Vector2f(0F, 0F)
    val scale = Vector2f(400F, 220F)
    private val mouse = Vector2f(0F, 0F)
    private val drag = Vector2f(0F, 0F)

    // idk
    val isThis
        get() = mc.currentScreen == this

    private var dragging = false
    private val categories = ModuleCategory.entries.map { CategoryButton(it) }
    var currentScreen = ModuleCategory.COMBAT.screen

    override fun initGui() {

        currentScreen.init()
    }

    /**
     * Only call this in NanoVG render event
     **/
    fun render() {
        if (dragging) {
            pos.x = mouse.x + drag.x
            pos.y = mouse.y + drag.y
        }

        scaleAnimation.run(if (isThis) 1.0 else 0.0)
        animatedScale = scaleAnimation.value

        if (isThis && animatedScale == 0.0) animatedScale = 0.01

        if (animatedScale == 0.0) {
            BloomClient.moduleManager[ModuleClickGUI::class.java]?.toggle()
            return
        }

        val translateX = ((pos.x + scale.x) / 2) * (1 - animatedScale)
        val translateY = ((pos.y + scale.y) / 2) * (1 - animatedScale)

        if (animatedScale != 1.0) {
            translate(translateX, translateY)
            scale(animatedScale)
        }

        roundedRect(pos.x, pos.y, scale.x, scale.y, 15, Color(50, 50, 50, 250))
        icon12Font.string("Q", pos.x + 10, pos.y + 10, Color(132, 115, 201))
        titleFont.string("Bloom", pos.x + icon12Font.width("Q") + 15, pos.y + 25, Color(132, 115, 201))

        val targetPosition = 20.0 * categories.indexOfFirst { it.category.screen == currentScreen }
        categoryAnimation.run(targetPosition)

        val animatedPosition = categoryAnimation.value.toFloat()
        roundedRect(pos.x + 10, pos.y + 50 + animatedPosition, 80, 15, 5, Color(200, 200, 200, 120))

        var offset = 0F
        for (c in categories) {
            c.pos.x = pos.x + 10
            c.pos.y = pos.y + 50 + offset
            c.render(mouse.x, mouse.y)
            offset += 20
        }

        currentScreen.pos.x = pos.x + 95
        currentScreen.pos.y = pos.y + 20

        scissor(
            (pos.x * animatedScale + translateX),
            (pos.y * animatedScale + translateY),
            (scale.x * animatedScale),
            (scale.y * animatedScale)
        ) {
            currentScreen.render(mouse.x, mouse.y)
        }
    }

    override fun drawScreen(mouseX: Int, mouseY: Int, partialTicks: Float) {
        mouse.x = mouseX.toFloat()
        mouse.y = mouseY.toFloat()
    }

    override fun mouseClicked(mouseX: Int, mouseY: Int, button: Int) {

        val mouseXf = mouseX.toFloat()
        val mouseYf = mouseY.toFloat()

        if (mouseOver(mouseXf, mouseYf, pos.x, pos.y, scale.x, 15F) && button == 0) {
            drag.x = pos.x - mouseXf
            drag.y = pos.y - mouseYf
            dragging = true
        }

        for (c in categories) {
            c.click(mouseXf, mouseYf, button)
        }

        currentScreen.click(mouseXf, mouseYf, button)
    }

    override fun mouseReleased(mouseX: Int, mouseY: Int, state: Int) {
        if (dragging) dragging = false

        val mouseXf = mouseX.toFloat()
        val mouseYf = mouseY.toFloat()

        for (c in categories) {
            c.release(mouseXf, mouseYf, state)
        }

        currentScreen.release(mouseXf, mouseYf, state)
    }

    override fun onGuiClosed() {
        super.onGuiClosed()
        dragging = false
    }

    /**
     * Switch screen when?
     **/
    fun switch(category: ModuleCategory) {
        if (category.screen == currentScreen) return
        currentScreen = category.screen
        currentScreen.init()
    }
}