package net.bloom.bloomclient.ui.clickgui.rise.button.value

import net.bloom.bloomclient.utils.MathUtils.mouseOver
import net.bloom.bloomclient.utils.animations.Animation
import net.bloom.bloomclient.utils.animations.Easing
import net.bloom.bloomclient.utils.render.NanoVGUtils.circle
import net.bloom.bloomclient.value.values.BoolValue
import java.awt.Color

class Switch(value: BoolValue) : ValueButton(value) {
    private val scaleAnimation = Animation(Easing.EASE_IN_BACK, 500)

    init {
        scale.x = f8.width(value.name) + 15F
        scale.y = f8.height.toFloat()
    }

    override fun render(mouseX: Float, mouseY: Float) {
        super.render(mouseX, mouseY)

        if (value is BoolValue) {
            // Calculate circle position
            val circleX = pos.x + f8.width(value.name) + 10
            val circleY = pos.y + f8.height / 2

            // Draw the switch label
            f8.string(value.name, pos.x, pos.y, Color(200, 200, 200, 250))

            // Draw background circle with animated scale
            circle(
                circleX, circleY, 5,
                Color(200, 200, 200, 250)
            )

            // Draw indicator circle when switch is on
            if (value.get()) {
                circle(
                    circleX, circleY, 5,
                    Color(100, 200, 100, 250)
                )
            }
        }
    }

    override fun click(mouseX: Float, mouseY: Float, button: Int) {
        super.click(mouseX, mouseY, button)

        if (value is BoolValue) {
            if (mouseOver(mouseX, mouseY, pos.x, pos.y, scale.x, scale.y)) {
                value.set(!value.get())
            }
        }
    }
}