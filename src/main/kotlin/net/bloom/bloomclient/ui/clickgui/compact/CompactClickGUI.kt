package net.bloom.bloomclient.ui.clickgui.compact

import net.bloom.bloomclient.BloomClient
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.font.Fonts
import net.bloom.bloomclient.utils.MathUtils
import net.bloom.bloomclient.utils.render.NanoVGUtils
import net.bloom.bloomclient.value.values.BoolValue
import net.bloom.bloomclient.value.values.FloatValue
import net.bloom.bloomclient.value.values.IntegerValue
import net.bloom.bloomclient.value.values.ListValue
import net.minecraft.client.gui.GuiScreen
import org.lwjgl.input.Mouse
import java.awt.Color

/**
 * A UI for Bloom Client
 * @author: tlowng(longathelstan)
 * the latest update: redesign all
 */
object CompactClickGUI : GuiScreen() {

    // --- GUI Dimensions ---
    private const val GUI_WIDTH = 470f
    private const val GUI_HEIGHT = 280f
    private const val GUI_RADIUS = 8f

    // Title Bar
    private const val TITLE_BAR_HEIGHT = 31f

    // Panels
    private const val CATEGORY_PANEL_WIDTH = 103f
    private const val MODULE_PANEL_WIDTH = 125f

    // --- Colors (Unified) ---
    private val BG_COLOR = Color(34, 38, 43)
    private val TITLE_BAR_COLOR = Color(0x12, 0x14, 0x16)
    private val HIGHLIGHT_COLOR = Color(0x19, 0x18, 0x18)
    private val TEXT_COLOR = Color(0xdf, 0xe2, 0xe7)
    private val TRANSPARENT = Color(0, 0, 0, 0)
    private val BOOL_ON_COLOR = Color(133, 196, 81)
    private val BOOL_OFF_COLOR = Color(212, 52, 65)
    private val SLIDER_BG_COLOR = Color(60, 60, 60)
    private val SLIDER_PROGRESS_COLOR = Color(132, 115, 201)

    // --- Dragging and Scrolling ---
    private var guiX = 50f
    private var guiY = 50f
    private var draggingGUI = false
    private var dragOffsetX = 0f
    private var dragOffsetY = 0f

    private var categoryScroll = 0f
    private var moduleScroll = 0f
    private var valueScroll = 0f

    private var draggingSlider = false

    // --- Current Selection ---
    private var currentCategory = ModuleCategory.COMBAT
    private var currentModule: Module? = null

    // --- Fonts ---
    private val titleFont = Fonts.fontNano[16]       // ~16px
    private val categoryFont = Fonts.fontLexend[9]   // ~9px
    private val moduleFont = Fonts.fontLexend[9]     // ~9px
    private val settingFont = Fonts.fontLexend[8]    // ~8px
    private val icon12Font = Fonts.fontIconGUI[12]     // ~12px
    private val icon24Font = Fonts.fontIconGUI[24]     // ~24px
    private val icon16Font = Fonts.fontIconGUI[16]

    // --- Row Heights & Spacing ---
    private const val MODULE_ROW_HEIGHT = 19f
    private const val SETTING_ROW_HEIGHT = 16f
    private const val CAT_SPACING = 2f
    private const val CATEGORY_TOP_MARGIN = 8f
    private const val MODULE_TOP_MARGIN = 8f
    private const val MODULE_SPACING = 2f

    // --- Icon Strings (Custom Font) ---
    private const val bloomIcon = "Q"
    private const val combatIcon = "R"
    private const val moveIcon = "S"
    private const val userIcon = "T"
    private const val searchIcon = "U"
    private const val eyeIcon = "V"
    private const val settingsIcon = "W"
    private const val hiddenEyeIcon = "X"
    private const val alertIcon = "Y"
    private const val checkIcon = "Z"
    private const val puzzleIcon = "B" // fallback

    private val listValueDropdownOpen = mutableMapOf<ListValue, Boolean>()
    private val listValueClickMap = mutableMapOf<ListValue, Boolean>()

    // --- Helper Functions ---

    private fun getCategoryDisplayName(cat: ModuleCategory): String =
        cat.name.lowercase().replaceFirstChar { it.titlecase() }

    private fun getCategoryIcon(cat: ModuleCategory): String = when (cat) {
        ModuleCategory.COMBAT   -> combatIcon
        ModuleCategory.MOVEMENT -> moveIcon
        ModuleCategory.PLAYER   -> userIcon
        ModuleCategory.RENDER   -> eyeIcon
        ModuleCategory.CLIENT   -> bloomIcon
        else                    -> puzzleIcon
    }

    // --- Rendering ---

    override fun drawScreen(mouseX: Int, mouseY: Int, partialTicks: Float) {
        NanoVGUtils.setup()
        NanoVGUtils.save()

        if (draggingGUI && Mouse.isButtonDown(0)) {
            guiX = mouseX - dragOffsetX
            guiY = mouseY - dragOffsetY
        } else {
            draggingGUI = false
        }

        NanoVGUtils.roundedOutlineRect(
            guiX, guiY, GUI_WIDTH, GUI_HEIGHT, 5f, GUI_RADIUS,
            BG_COLOR, BG_COLOR
        )

        NanoVGUtils.rect(
            guiX, guiY, GUI_WIDTH, TITLE_BAR_HEIGHT, GUI_RADIUS,
            TITLE_BAR_COLOR
        )

        icon24Font.string(
            bloomIcon,
            guiX + 10f,
            guiY + (TITLE_BAR_HEIGHT - icon24Font.height) / 2f,
            Color.WHITE
        )
        icon16Font.string(
            searchIcon,
            guiX + GUI_WIDTH - 30f,
            guiY + (TITLE_BAR_HEIGHT - icon16Font.height) / 2f,
            Color.WHITE
        )
        titleFont.string(
            "bloom",
            guiX + 37f,
            guiY + (TITLE_BAR_HEIGHT - titleFont.height) / 2f + 2f,
            Color.WHITE
        )

        drawCategoryPanel(mouseX, mouseY)
        NanoVGUtils.rect(
            guiX + CATEGORY_PANEL_WIDTH,
            guiY + TITLE_BAR_HEIGHT,
            2f, GUI_HEIGHT - TITLE_BAR_HEIGHT,
            color = TITLE_BAR_COLOR
        )
        drawModulePanel(mouseX, mouseY)
        NanoVGUtils.rect(
            guiX + CATEGORY_PANEL_WIDTH + 2f + MODULE_PANEL_WIDTH,
            guiY + TITLE_BAR_HEIGHT,
            2f, GUI_HEIGHT - TITLE_BAR_HEIGHT,
            color = TITLE_BAR_COLOR
        )
        currentModule?.let { drawSettingsPanel(it, mouseX, mouseY) }

        NanoVGUtils.restore()
        NanoVGUtils.close()
    }

    private fun drawCategoryPanel(mouseX: Int, mouseY: Int) {
        val x = guiX
        val y = guiY + TITLE_BAR_HEIGHT + CATEGORY_TOP_MARGIN
        val w = CATEGORY_PANEL_WIDTH
        val h = GUI_HEIGHT - TITLE_BAR_HEIGHT - CATEGORY_TOP_MARGIN
        val categories = ModuleCategory.values()
        val totalCatHeight = categories.size * (MODULE_ROW_HEIGHT + CAT_SPACING)
        val minScroll = -(totalCatHeight - h).coerceAtLeast(0f)
        val maxScroll = 0f

        if (MathUtils.isHover(mouseX, mouseY, x, y, x + w, y + h)) {
            val dWheel = Mouse.getDWheel() * 0.2f
            categoryScroll = (categoryScroll + dWheel).coerceIn(minScroll, maxScroll)
        }

        NanoVGUtils.scissor(x, y, w, h) {
            categories.forEachIndexed { i, cat ->
                val yPos = y + categoryScroll + i * (MODULE_ROW_HEIGHT + CAT_SPACING)
                if (yPos + MODULE_ROW_HEIGHT < y || yPos > y + h) return@forEachIndexed

                val hovered = MathUtils.isHover(mouseX, mouseY, x, yPos, x + w - 10f, yPos + MODULE_ROW_HEIGHT)
                val active = (cat == currentCategory)
                val bgColor = if (active || hovered) HIGHLIGHT_COLOR else TRANSPARENT

                NanoVGUtils.rect(x + 4f, yPos, w - 10f, MODULE_ROW_HEIGHT, 8f, bgColor)
                val iconStr = getCategoryIcon(cat)
                icon12Font.string(
                    iconStr,
                    x + 8f,
                    yPos + (MODULE_ROW_HEIGHT - icon12Font.height) / 2f,
                    TEXT_COLOR
                )
                val iconWidth = icon12Font.width(iconStr)
                val displayName = getCategoryDisplayName(cat)
                categoryFont.string(
                    displayName,
                    x + 8f + iconWidth + 4f,
                    yPos + (MODULE_ROW_HEIGHT - categoryFont.height) / 2f + 0.5f,
                    TEXT_COLOR
                )
            }
        }
    }

    private fun drawModulePanel(mouseX: Int, mouseY: Int) {
        val x = guiX + CATEGORY_PANEL_WIDTH + 2f
        val y = guiY + TITLE_BAR_HEIGHT
        val w = MODULE_PANEL_WIDTH
        val h = GUI_HEIGHT - TITLE_BAR_HEIGHT

        val modules = BloomClient.moduleManager.modules
            .filter { it.category == currentCategory }
            .sortedBy { it.displayName }
        val totalModHeight = modules.size * (MODULE_ROW_HEIGHT + MODULE_SPACING)
        val minScroll = -(totalModHeight - h).coerceAtLeast(0f)
        val maxScroll = 0f

        if (MathUtils.isHover(mouseX, mouseY, x, y, x + w, y + h)) {
            val dWheel = Mouse.getDWheel() * 0.2f
            moduleScroll = (moduleScroll + dWheel).coerceIn(minScroll, maxScroll)
        }

        NanoVGUtils.scissor(x, y, w, h) {
            modules.forEachIndexed { i, mod ->
                val yPos = y + MODULE_TOP_MARGIN + moduleScroll + i * (MODULE_ROW_HEIGHT + MODULE_SPACING)
                if (yPos + MODULE_ROW_HEIGHT < y || yPos > y + h) return@forEachIndexed

                val hovered = MathUtils.isHover(mouseX, mouseY, x, yPos, x + w, yPos + MODULE_ROW_HEIGHT)
                val selected = (mod == currentModule)
                val bgColor = if (selected || hovered) HIGHLIGHT_COLOR else TRANSPARENT

                NanoVGUtils.rect(x + 5f, yPos, w - 10f, MODULE_ROW_HEIGHT, 2f, bgColor)
                val textColor = if (mod.state) BOOL_ON_COLOR else TEXT_COLOR
                moduleFont.string(
                    mod.displayName,
                    x + 12f,
                    yPos + (MODULE_ROW_HEIGHT - moduleFont.height) / 2f,
                    textColor
                )
            }
        }
    }

    private fun drawSettingsPanel(module: Module, mouseX: Int, mouseY: Int) {
        val x = guiX + CATEGORY_PANEL_WIDTH + 2f + MODULE_PANEL_WIDTH + 5f
        val y = guiY + TITLE_BAR_HEIGHT
        val w = GUI_WIDTH - (CATEGORY_PANEL_WIDTH + 2f + MODULE_PANEL_WIDTH + 2f)
        val h = GUI_HEIGHT - TITLE_BAR_HEIGHT

        settingFont.string(
            "${module.displayName} Settings",
            x + 8f,
            y + 4f,
            TEXT_COLOR
        )

        val values = module.displayableValues
        var offsetY = 16f
        val totalValHeight = values.size * SETTING_ROW_HEIGHT
        val minScroll = -(totalValHeight - (h - offsetY)).coerceAtLeast(0f)
        val maxScroll = 0f

        if (MathUtils.isHover(mouseX, mouseY, x, y, x + w, y + h)) {
            val dWheel = Mouse.getDWheel() * 0.2f
            valueScroll = (valueScroll + dWheel).coerceIn(minScroll, maxScroll)
        }

        NanoVGUtils.scissor(x - 2, y, w, h) {
            NanoVGUtils.rect(x - 2f, y + settingFont.height + 8f, w - 2f, h - 15f, 2f, HIGHLIGHT_COLOR)

            values.forEachIndexed { _, v ->
                val yPos = y + 5f + offsetY + valueScroll

                if (yPos + SETTING_ROW_HEIGHT < y || yPos > y + h) {
                    offsetY += SETTING_ROW_HEIGHT
                    return@forEachIndexed
                }

                settingFont.string(
                    v.name,
                    x + 10f,
                    yPos + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
                    TEXT_COLOR
                )

                when (v) {
                    is BoolValue -> drawBoolValue(v, x + 120f, yPos)
                    is IntegerValue -> drawIntValue(v, x + 120f, yPos, mouseX, mouseY)
                    is FloatValue -> drawFloatValue(v, x + 120f, yPos, mouseX, mouseY)
                    is ListValue -> drawListValue(v, x + 120f, yPos, mouseX, mouseY)
                }
                offsetY += SETTING_ROW_HEIGHT
            }
        }
    }

    // --- Individual Value Renders ---

    private fun drawBoolValue(value: BoolValue, x: Float, y: Float) {
        val isOn = value.value
        val bgColor = if (isOn) BOOL_ON_COLOR else BOOL_OFF_COLOR
        NanoVGUtils.rect(x, y + 2f, 40f, SETTING_ROW_HEIGHT - 4f, 2f, bgColor)
        settingFont.string(
            if (isOn) "ON" else "OFF",
            x + 12f,
            y + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
            Color.WHITE
        )
    }

    private fun drawIntValue(value: IntegerValue, x: Float, y: Float, mouseX: Int, mouseY: Int) {
        drawSlider(
            current = value.value.toFloat(),
            min = value.minRange.toFloat(),
            max = value.maxRange.toFloat(),
            x = x, y = y,
            mouseX = mouseX, mouseY = mouseY
        ) { newVal -> value.set(newVal.toInt()) }
    }

    private fun drawFloatValue(value: FloatValue, x: Float, y: Float, mouseX: Int, mouseY: Int) {
        drawSlider(
            current = value.value,
            min = value.minRange,
            max = value.maxRange,
            x = x, y = y,
            mouseX = mouseX, mouseY = mouseY
        ) { newVal -> value.set(newVal) }
    }

    private fun drawListValue(value: ListValue, x: Float, y: Float, mouseX: Int, mouseY: Int) {
        val isOpen = listValueDropdownOpen.getOrDefault(value, false)
        val text = value.value
        val textWidth = settingFont.width(text)
        val boxWidth = textWidth + 30f

        NanoVGUtils.rect(x, y, boxWidth, SETTING_ROW_HEIGHT, 2f, HIGHLIGHT_COLOR)
        settingFont.string(text, x + 10f, y + (SETTING_ROW_HEIGHT - settingFont.height) / 2f, TEXT_COLOR)
        settingFont.string("v", x + boxWidth - 15f, y + (SETTING_ROW_HEIGHT - settingFont.height) / 2f, TEXT_COLOR)

        if (MathUtils.isHover(mouseX, mouseY, x, y, x + boxWidth, y + SETTING_ROW_HEIGHT) && Mouse.isButtonDown(0)) {
            if (listValueClickMap.getOrDefault(value, false).not()) {
                listValueDropdownOpen[value] = !isOpen
                listValueClickMap[value] = true
            }
        } else {
            listValueClickMap[value] = false
        }

        if (listValueDropdownOpen.getOrDefault(value, false)) {
            value.values.forEachIndexed { index, option ->
                val optionY = y + (index + 1) * SETTING_ROW_HEIGHT
                val optionHovered = MathUtils.isHover(mouseX, mouseY, x, optionY, x + boxWidth, optionY + SETTING_ROW_HEIGHT)
                val optionBgColor = if (optionHovered)  TRANSPARENT else HIGHLIGHT_COLOR
                NanoVGUtils.rect(x, optionY, boxWidth, SETTING_ROW_HEIGHT, 0f, optionBgColor)
                settingFont.string(
                    option,
                    x + 10f,
                    optionY + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
                    TEXT_COLOR
                )

                if (optionHovered && Mouse.isButtonDown(0)) {
                    value.set(option)
                    listValueDropdownOpen[value] = false
                }
            }
        }
    }


    private fun drawSlider(
        current: Float,
        min: Float,
        max: Float,
        x: Float,
        y: Float,
        mouseX: Int,
        mouseY: Int,
        setter: (Float) -> Unit
    ) {
        val sliderWidth = 80f
        val barHeight = 3f
        val progress = (current - min) / (max - min).coerceAtLeast(0.00001f)

        NanoVGUtils.rect(
            x,
            y + (SETTING_ROW_HEIGHT - barHeight) / 2f,
            sliderWidth,
            barHeight,
            color = SLIDER_BG_COLOR
        )
        NanoVGUtils.rect(
            x,
            y + (SETTING_ROW_HEIGHT - barHeight) / 2f,
            sliderWidth * progress,
            barHeight,
            color = SLIDER_PROGRESS_COLOR
        )

        val display = String.format("%.1f", current)
        settingFont.string(
            display,
            x + sliderWidth + 5f,
            y + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
            TEXT_COLOR
        )

        if (draggingSlider && Mouse.isButtonDown(0)) {
            if (MathUtils.isHover(mouseX, mouseY, x, y, x + sliderWidth, y + SETTING_ROW_HEIGHT)) {
                val newVal = ((mouseX - x) / sliderWidth) * (max - min) + min
                setter(newVal.coerceIn(min, max))
            }
        }
    }

    // --- Mouse Handling ---

    override fun mouseClicked(mouseX: Int, mouseY: Int, mouseButton: Int) {
        if (MathUtils.isHover(mouseX, mouseY, guiX, guiY, guiX + GUI_WIDTH, guiY + TITLE_BAR_HEIGHT) &&
            mouseButton == 0
        ) {
            draggingGUI = true
            dragOffsetX = mouseX - guiX
            dragOffsetY = mouseY - guiY
        }

        val catX = guiX
        val catY = guiY + TITLE_BAR_HEIGHT + CATEGORY_TOP_MARGIN
        val catW = CATEGORY_PANEL_WIDTH
        ModuleCategory.values().forEachIndexed { i, cat ->
            val yPos = catY + categoryScroll + i * (MODULE_ROW_HEIGHT + CAT_SPACING)
            if (MathUtils.isHover(mouseX, mouseY, catX, yPos, catX + catW, yPos + MODULE_ROW_HEIGHT)) {
                currentCategory = cat
                currentModule = null
                moduleScroll = 0f
            }
        }

        val modX = guiX + CATEGORY_PANEL_WIDTH + 2f
        val modY = guiY + TITLE_BAR_HEIGHT
        BloomClient.moduleManager.modules
            .filter { it.category == currentCategory }
            .sortedBy { it.displayName }
            .forEachIndexed { i, m ->
                val yPos = modY + MODULE_TOP_MARGIN + moduleScroll + i * (MODULE_ROW_HEIGHT + MODULE_SPACING)
                if (MathUtils.isHover(mouseX, mouseY, modX, yPos, modX + MODULE_PANEL_WIDTH, yPos + MODULE_ROW_HEIGHT)) {
                    when (mouseButton) {
                        0 -> m.toggle()  // left-click: toggle
                        1 -> currentModule = if (currentModule == m) null else m  // right-click: mở settings
                    }
                }
            }

        if (mouseButton == 0) draggingSlider = true
    }

    override fun mouseReleased(mouseX: Int, mouseY: Int, state: Int) {
        draggingGUI = false
        draggingSlider = false
    }

    override fun doesGuiPauseGame() = false
}
