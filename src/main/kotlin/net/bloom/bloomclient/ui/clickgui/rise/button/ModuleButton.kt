package net.bloom.bloomclient.ui.clickgui.rise.button

import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.ui.clickgui.rise.button.value.ValueButton
import net.bloom.bloomclient.utils.MathUtils.mouseOver
import net.bloom.bloomclient.utils.animations.Animation
import net.bloom.bloomclient.utils.animations.Easing
import net.bloom.bloomclient.utils.render.NanoVGUtils.roundedRect
import org.lwjgl.input.Keyboard
import java.awt.Color

/**
 * Module button, I'll add values setting latter
 **/
class ModuleButton(val module: Module) : Button() {
    private val openAnimation = Animation(Easing.EASE_IN_OUT_EXPO, 500)
    private var values = listOf<ValueButton>()
    private var expanded = false

    init {
        scale.x = 290F
        scale.y = 40F

        openAnimation.value = scale.y.toDouble()
        values = module.values.map { it.createButton() }
    }

    override fun render(mouseX: Float, mouseY: Float) {
        val baseHeight = 40F
        var height = baseHeight

        roundedRect(pos.x, pos.y, scale.x, scale.y, 5, Color(132, 115, 201))
        f10.string(module.name, pos.x + 5, pos.y + 5, Color(240, 240, 240, 250))
        f8.string(module.description, pos.x + 5, pos.y + 25, Color(180, 180, 180, 250))

        roundedRect(
            pos.x + f12.width(module.name) + 13,
            pos.y + 4, f8.width(Keyboard.getKeyName(module.keyBind)) + 5,
            f10.height + 2, 3, Color(180, 180, 180, 250)
        )
        f8.string(
            Keyboard.getKeyName(module.keyBind),
            pos.x + f12.width(module.name) + 15,
            pos.y + 7, Color(240, 240, 240, 250)
        )

        if (expanded) {
            var offset = baseHeight + 5

            for (v in values) {
                v.pos.x = pos.x + 10
                v.pos.y = pos.y + offset

                v.render(mouseX, mouseY)

                offset += v.scale.y + 5
                height += v.scale.y + 5
            }
        }

        openAnimation.run(
            if (expanded)
                height.toDouble() + 10
            else
                baseHeight.toDouble()
        )

        scale.y = openAnimation.value.toFloat()
    }

    override fun click(mouseX: Float, mouseY: Float, button: Int) {
        if (mouseOver(mouseX, mouseY, pos.x, pos.y, scale.x, 40F)) {
            when (button) {
                0 -> module.toggle()
                1 -> expanded = !expanded
            }
        }

        if (expanded) {
            for (v in values) {
                if (!v.value.canDisplay()) continue
                v.click(mouseX, mouseY, button)
            }
        }
    }

    override fun release(mouseX: Float, mouseY: Float, button: Int) {
        if (expanded) {
            for (v in values) {
                if (!v.value.canDisplay()) continue
                v.release(mouseX, mouseY, button)
            }
        }
    }
}