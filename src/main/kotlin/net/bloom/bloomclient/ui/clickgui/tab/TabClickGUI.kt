package net.bloom.bloomclient.ui.clickgui.tab

import net.bloom.bloomclient.BloomClient
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.font.Fonts
import net.bloom.bloomclient.utils.MathUtils
import net.bloom.bloomclient.utils.animations.EaseUtils
import net.bloom.bloomclient.utils.render.NanoVGUtils
import net.bloom.bloomclient.value.values.BoolValue
import net.bloom.bloomclient.value.values.FloatValue
import net.bloom.bloomclient.value.values.IntegerValue
import net.bloom.bloomclient.value.values.ListValue
import net.minecraft.client.gui.GuiScreen
import org.lwjgl.input.Keyboard
import org.lwjgl.input.Mouse
import java.awt.Color
import kotlin.math.*

/**
 * A modern ClickGUI for Bloom Client
 * @author: tlowng(longathelstan) - Original
 * Redesigned and improved version
 */
object TabClickGUI : GuiScreen() {

    // --- GUI Dimensions ---
    private const val GUI_WIDTH = 470f
    private const val GUI_HEIGHT = 280f
    private const val GUI_RADIUS = 8f

    // Title Bar
    private const val TITLE_BAR_HEIGHT = 31f

    // Panels
    private const val CATEGORY_PANEL_WIDTH = 103f
    private const val MODULE_PANEL_WIDTH = 125f

    // --- Colors (Theme) ---
    private val BG_COLOR = Color(28, 32, 38)
    private val TITLE_BAR_COLOR = Color(14, 16, 19)
    private val HIGHLIGHT_COLOR = Color(36, 40, 46)
    private val HOVER_COLOR = Color(44, 48, 54)
    private val TEXT_COLOR = Color(220, 225, 230)
    private val SUBTITLE_COLOR = Color(180, 185, 190)
    private val TRANSPARENT = Color(0, 0, 0, 0)
    private val BOOL_ON_COLOR = Color(133, 196, 81)
    private val BOOL_OFF_COLOR = Color(212, 52, 65)
    private val SLIDER_BG_COLOR = Color(60, 63, 67)
    private val SLIDER_PROGRESS_COLOR = Color(132, 115, 201)
    private val ACCENT_COLOR = Color(132, 115, 201)
    private val SEARCH_BAR_COLOR = Color(24, 27, 32)

    // --- Dragging and Scrolling ---
    private var guiX = 50f
    private var guiY = 50f
    private var draggingGUI = false
    private var dragOffsetX = 0f
    private var dragOffsetY = 0f

    private var categoryScroll = 0f
    private var moduleScroll = 0f
    private var valueScroll = 0f
    private val scrollSpeed = 20f

    private var categoryScrollTarget = 0f
    private var moduleScrollTarget = 0f
    private var valueScrollTarget = 0f

    private const val SCROLL_ANIMATION_SPEED = 0.15f

    private var draggingSliderValue: Any? = null
    private var searchActive = false
    private var searchQuery = ""

    // Animations
    private val moduleStateAnimations = mutableMapOf<Module, Float>()
    private val categoryHoverAnimations = mutableMapOf<ModuleCategory, Float>()
    private val sliderHandleAnimations = mutableMapOf<Any, Float>()
    private val booleanValueAnimations = mutableMapOf<BoolValue, Float>()
    private var searchBarAnimation = 0f
    private var searchBarAnimationTarget = 0f
    private val SEARCH_ANIMATION_SPEED = 0.15f

    // --- Current Selection ---
    private var currentCategory = ModuleCategory.COMBAT
    private var currentModule: Module? = null

    // --- Fonts ---
    private val titleFont = Fonts.fontNano[16]       // Title
    private val categoryFont = Fonts.fontLexend[10]   // Categories
    private val moduleFont = Fonts.fontLexend[10]     // Modules
    private val settingFont = Fonts.fontLexend[9]    // Settings
    private val settingHeaderFont = Fonts.fontLexend[10] // Setting headers
    private val icon12Font = Fonts.fontIconGUI[12]     // Small icons
    private val icon24Font = Fonts.fontIconGUI[24]     // Large icons
    private val icon16Font = Fonts.fontIconGUI[16]     // Medium icons

    // --- Row Heights & Spacing ---
    private const val MODULE_ROW_HEIGHT = 20f
    private const val SETTING_ROW_HEIGHT = 18f
    private const val CAT_SPACING = 2f
    private const val CATEGORY_TOP_MARGIN = 8f
    private const val MODULE_TOP_MARGIN = 8f
    private const val MODULE_SPACING = 2f
    private const val SETTING_SPACING = 7f

    // --- Icon Strings (Custom Font) ---
    private const val BLOOM_ICON = "Q"
    private const val COMBAT_ICON = "R"
    private const val MOVE_ICON = "S"
    private const val USER_ICON = "T"
    private const val SEARCH_ICON = "U"
    private const val EYE_ICON = "V"
    private const val SETTINGS_ICON = "W"
    private const val HIDDEN_EYE_ICON = "X"
    private const val ALERT_ICON = "Y"
    private const val CHECK_ICON = "Z"
    private const val PUZZLE_ICON = "B" // fallback

    private val listValueDropdownOpen = mutableMapOf<ListValue, Boolean>()
    private val listValueClickMap = mutableMapOf<ListValue, Boolean>()

    private var editingValue: Any? = null
    private var editingText = ""
    private var editingCursorBlink = 0L
    private val valueEditMap = mutableMapOf<Any, Boolean>()

    // --- Helper Functions ---

    private fun getCategoryDisplayName(cat: ModuleCategory): String =
        cat.name.lowercase().replaceFirstChar { it.titlecase() }

    private fun getCategoryIcon(cat: ModuleCategory): String = when (cat) {
        ModuleCategory.COMBAT   -> COMBAT_ICON
        ModuleCategory.MOVEMENT -> MOVE_ICON
        ModuleCategory.PLAYER   -> USER_ICON
        ModuleCategory.RENDER   -> EYE_ICON
        ModuleCategory.CLIENT   -> BLOOM_ICON
        else                    -> PUZZLE_ICON
    }

    private fun ensureModuleAnimationExists(module: Module) {
        if (!moduleStateAnimations.containsKey(module)) {
            moduleStateAnimations[module] = if (module.state) 1f else 0f
        }
    }

    private fun ensureCategoryAnimationExists(category: ModuleCategory) {
        if (!categoryHoverAnimations.containsKey(category)) {
            categoryHoverAnimations[category] = if (category == currentCategory) 1f else 0f
        }
    }

    private fun ensureBoolAnimationExists(value: BoolValue) {
        if (!booleanValueAnimations.containsKey(value)) {
            booleanValueAnimations[value] = if (value.value) 1f else 0f
        }
    }

    private fun getFilteredModules(): List<Module> {
        val modules = BloomClient.moduleManager.modules

        return if (searchActive && searchQuery.isNotEmpty()) {
            modules.filter { it.displayName.lowercase().contains(searchQuery.lowercase()) }
                .sortedBy { it.displayName }
        } else {
            modules.filter { it.category == currentCategory }
                .sortedBy { it.displayName }
        }
    }


    override fun drawScreen(mouseX: Int, mouseY: Int, partialTicks: Float) {
        updateAnimations()

        NanoVGUtils.setup()
        NanoVGUtils.save()

        if (draggingGUI && Mouse.isButtonDown(0)) {
            guiX = mouseX - dragOffsetX
            guiY = mouseY - dragOffsetY
        } else {
            draggingGUI = false
        }

        NanoVGUtils.drawShadowOutlineRoundedRect(
            guiX, guiY, GUI_WIDTH, GUI_HEIGHT, 10f,
            GUI_RADIUS, 15f, 0.3f, BG_COLOR, BG_COLOR
        )

        NanoVGUtils.rect(
            guiX, guiY, GUI_WIDTH, TITLE_BAR_HEIGHT,
            GUI_RADIUS, TITLE_BAR_COLOR
        )

        /*
        // Draw bottom part to flatten title bar bottom corners
        NanoVGUtils.drawRect(
            guiX, guiY + TITLE_BAR_HEIGHT - 5f,
            GUI_WIDTH, 5f,
            TITLE_BAR_COLOR
        )*/

        icon24Font.string(
            BLOOM_ICON,
            guiX + 12f,
            guiY + (TITLE_BAR_HEIGHT - icon24Font.height) / 2f,
            ACCENT_COLOR
        )

        titleFont.string(
            "bloom",
            guiX + 42f,
            guiY + (TITLE_BAR_HEIGHT - titleFont.height) / 2f + 1f,
            TEXT_COLOR
        )

        drawSearchBar(mouseX, mouseY)
        if (!searchActive || searchQuery.isEmpty())
            drawCategoryPanel(mouseX, mouseY)

        NanoVGUtils.rect(
            guiX + CATEGORY_PANEL_WIDTH,
            guiY + TITLE_BAR_HEIGHT,
            1f, GUI_HEIGHT - TITLE_BAR_HEIGHT,
            color = TITLE_BAR_COLOR
        )

        drawModulePanel(mouseX, mouseY)

        NanoVGUtils.rect(
            guiX + CATEGORY_PANEL_WIDTH + MODULE_PANEL_WIDTH,
            guiY + TITLE_BAR_HEIGHT,
            1f, GUI_HEIGHT - TITLE_BAR_HEIGHT,
            color = TITLE_BAR_COLOR
        )

        currentModule?.let { drawSettingsPanel(it, mouseX, mouseY) }

        NanoVGUtils.restore()
        NanoVGUtils.close()
    }

    private fun drawSearchBar(mouseX: Int, mouseY: Int) {
        val searchBarFullWidth = 140f
        val searchBarIconWidth = 22f
        val searchBarHeight = 22f

        val targetWidth = if (searchActive) searchBarFullWidth else searchBarIconWidth
        val animatedWidth = searchBarFullWidth * searchBarAnimation + searchBarIconWidth * (1 - searchBarAnimation)

        val searchBarX = guiX + GUI_WIDTH - animatedWidth - 12f
        val searchBarY = guiY + (TITLE_BAR_HEIGHT - searchBarHeight) / 2f

        val isHovered = MathUtils.isHover(mouseX, mouseY, searchBarX, searchBarY, searchBarX + animatedWidth, searchBarY + searchBarHeight)

        searchBarAnimationTarget = if (searchActive) 1f else 0f

        if (searchBarAnimation < searchBarAnimationTarget) {
            searchBarAnimation = min(searchBarAnimation + SEARCH_ANIMATION_SPEED, searchBarAnimationTarget)
        } else if (searchBarAnimation > searchBarAnimationTarget) {
            searchBarAnimation = max(searchBarAnimation - SEARCH_ANIMATION_SPEED, searchBarAnimationTarget)
        }

        val easedAnimation = EaseUtils.easeOutCirc(searchBarAnimation.toDouble()).toFloat()

        val bgColorBase = SEARCH_BAR_COLOR
        val bgColorTarget = Color(
            min(bgColorBase.red + 20, 255),
            min(bgColorBase.green + 20, 255),
            min(bgColorBase.blue + 20, 255)
        )

        val r = bgColorBase.red + ((bgColorTarget.red - bgColorBase.red) * easedAnimation).toInt()
        val g = bgColorBase.green + ((bgColorTarget.green - bgColorBase.green) * easedAnimation).toInt()
        val b = bgColorBase.blue + ((bgColorTarget.blue - bgColorBase.blue) * easedAnimation).toInt()
        val bgColor = Color(r, g, b)

        NanoVGUtils.rect(
            searchBarX, searchBarY,
            animatedWidth, searchBarHeight,
            6f, bgColor
        )

        if (searchActive || easedAnimation > 0.1f) {
            val borderAlpha = (easedAnimation * 255).toInt()
            val borderColor = Color(
                ACCENT_COLOR.red,
                ACCENT_COLOR.green,
                ACCENT_COLOR.blue,
                borderAlpha
            )

            val borderThickness = 1f + 0.5f * easedAnimation
            NanoVGUtils.roundedOutlineRect(
                searchBarX, searchBarY,
                animatedWidth, searchBarHeight,
                borderThickness, 6f, bgColor, borderColor
            )
        }

        val iconBaseColor = SUBTITLE_COLOR
        val r2 = iconBaseColor.red + ((ACCENT_COLOR.red - iconBaseColor.red) * easedAnimation).toInt()
        val g2 = iconBaseColor.green + ((ACCENT_COLOR.green - iconBaseColor.green) * easedAnimation).toInt()
        val b2 = iconBaseColor.blue + ((ACCENT_COLOR.blue - iconBaseColor.blue) * easedAnimation).toInt()
        val iconColor = Color(r2, g2, b2)

        icon12Font.string(
            SEARCH_ICON,
            searchBarX + 5f,
            searchBarY + (searchBarHeight - icon12Font.height) / 2f,
            iconColor
        )

        if (easedAnimation > 0.3f) {
            val textOpacity = ((easedAnimation - 0.3f) / 0.7f).coerceIn(0f, 1f)
            val cursorVisible = if (searchActive) ((System.currentTimeMillis() / 500) % 2) == 0L else false
            val displayText = if (searchQuery.isEmpty() && !searchActive) "Search..." else searchQuery

            val placeholderBaseColor = SUBTITLE_COLOR
            val textTargetColor = TEXT_COLOR
            val isPlaceholder = searchQuery.isEmpty() && !searchActive

            val textColorAnimated = if (isPlaceholder) {
                Color(
                    placeholderBaseColor.red,
                    placeholderBaseColor.green,
                    placeholderBaseColor.blue,
                    (placeholderBaseColor.alpha * textOpacity).toInt()
                )
            } else {
                val r3 = placeholderBaseColor.red + ((textTargetColor.red - placeholderBaseColor.red) * easedAnimation).toInt()
                val g3 = placeholderBaseColor.green + ((textTargetColor.green - placeholderBaseColor.green) * easedAnimation).toInt()
                val b3 = placeholderBaseColor.blue + ((textTargetColor.blue - placeholderBaseColor.blue) * easedAnimation).toInt()
                Color(r3, g3, b3, (255 * textOpacity).toInt())
            }

            moduleFont.string(
                displayText,
                searchBarX + 26f,
                searchBarY + (searchBarHeight - moduleFont.height) / 2f,
                textColorAnimated
            )

            if (cursorVisible && searchActive) {
                val textWidth = moduleFont.width(searchQuery)
                val cursorAlpha = (sin(System.currentTimeMillis() / 200.0) * 0.5 + 0.5) * 255 * textOpacity
                val cursorColor = Color(
                    TEXT_COLOR.red,
                    TEXT_COLOR.green,
                    TEXT_COLOR.blue,
                    cursorAlpha.toInt()
                )

                NanoVGUtils.rect(
                    searchBarX + 26f + textWidth,
                    searchBarY + (searchBarHeight - moduleFont.height) / 2f,
                    1.5f,
                    moduleFont.height.toFloat(),
                    color = cursorColor
                )
            }

            if (searchQuery.isNotEmpty()) {
                val clearX = searchBarX + animatedWidth - 20f
                val clearY = searchBarY + searchBarHeight / 2f
                val clearHovered = MathUtils.isHover(mouseX, mouseY, clearX - 8f, clearY - 8f, clearX + 8f, clearY + 8f)

                val clearBaseColor = SUBTITLE_COLOR
                val clearHoverColor = TEXT_COLOR
                val clearAnimation = if (clearHovered) easedAnimation else 0f

                val r4 = clearBaseColor.red + ((clearHoverColor.red - clearBaseColor.red) * clearAnimation).toInt()
                val g4 = clearBaseColor.green + ((clearHoverColor.green - clearBaseColor.green) * clearAnimation).toInt()
                val b4 = clearBaseColor.blue + ((clearHoverColor.blue - clearBaseColor.blue) * clearAnimation).toInt()

                val clearColor = Color(r4, g4, b4, (255 * textOpacity).toInt())

                moduleFont.string(
                    "×",
                    clearX,
                    clearY - moduleFont.height / 2f,
                    clearColor
                )

                if (clearHovered && Mouse.isButtonDown(0)) {
                    searchQuery = ""
                    moduleScrollTarget = 0f
                    moduleScroll = 0f
                }
            }
        }

        if (Mouse.isButtonDown(0)) {
            if (MathUtils.isHover(mouseX, mouseY, searchBarX, searchBarY, searchBarX + animatedWidth, searchBarY + searchBarHeight)) {
                if (!searchActive) {
                    searchActive = true
                }
            } else if (searchBarAnimation >= 0.95f) {
                searchActive = false
            }
        }
    }

    private fun drawCategoryPanel(mouseX: Int, mouseY: Int) {
        val x = guiX
        val y = guiY + TITLE_BAR_HEIGHT + CATEGORY_TOP_MARGIN
        val w = CATEGORY_PANEL_WIDTH
        val h = GUI_HEIGHT - TITLE_BAR_HEIGHT - CATEGORY_TOP_MARGIN
        val categories = ModuleCategory.values()
        val totalCatHeight = categories.size * (MODULE_ROW_HEIGHT + CAT_SPACING)
        val minScroll = -(totalCatHeight - h).coerceAtLeast(0f)
        val maxScroll = 0f

        if (MathUtils.isHover(mouseX, mouseY, x, y, x + w, y + h)) {
            val dWheel = Mouse.getDWheel() * scrollSpeed
            categoryScrollTarget = (categoryScrollTarget + dWheel).coerceIn(minScroll, maxScroll)
        }

        NanoVGUtils.scissor(x, y, w, h) {
            categories.forEachIndexed { i, cat ->
                val yPos = y + categoryScroll + i * (MODULE_ROW_HEIGHT + CAT_SPACING)
                if (yPos + MODULE_ROW_HEIGHT < y || yPos > y + h) return@forEachIndexed

                ensureCategoryAnimationExists(cat)
                val hovered = MathUtils.isHover(mouseX, mouseY, x, yPos, x + w - 10f, yPos + MODULE_ROW_HEIGHT)
                val active = (cat == currentCategory)

                if (hovered && !active) {
                    categoryHoverAnimations[cat] = min(categoryHoverAnimations[cat]!! + 0.05f, 0.5f)
                } else if (!active) {
                    categoryHoverAnimations[cat] = max(categoryHoverAnimations[cat]!! - 0.05f, 0f)
                } else {
                    categoryHoverAnimations[cat] = min(categoryHoverAnimations[cat]!! + 0.05f, 1f)
                }

                val animationValue = categoryHoverAnimations[cat]!!
                val bgColor = if (active) {
                    Color(
                        ACCENT_COLOR.red,
                        ACCENT_COLOR.green,
                        ACCENT_COLOR.blue,
                        (60 + 40 * animationValue).toInt()
                    )
                } else if (hovered) {
                    Color(
                        HIGHLIGHT_COLOR.red,
                        HIGHLIGHT_COLOR.green,
                        HIGHLIGHT_COLOR.blue,
                        (100 * animationValue).toInt()
                    )
                } else {
                    TRANSPARENT
                }

                NanoVGUtils.rect(x + 6f, yPos, w - 12f, MODULE_ROW_HEIGHT, 4f, bgColor)

                val iconColor = if (active) ACCENT_COLOR else TEXT_COLOR
                val iconStr = getCategoryIcon(cat)
                icon12Font.string(
                    iconStr,
                    x + 16f,
                    yPos + (MODULE_ROW_HEIGHT - icon12Font.height) / 2f,
                    iconColor
                )

                val iconWidth = icon12Font.width(iconStr)
                val displayName = getCategoryDisplayName(cat)

                categoryFont.string(
                    displayName,
                    x + 16f + iconWidth + 8f,
                    yPos + (MODULE_ROW_HEIGHT - categoryFont.height) / 2f + 0.5f,
                    if (active) ACCENT_COLOR else TEXT_COLOR
                )

                if (active) {
                    NanoVGUtils.rect(
                        x + 8f,
                        yPos + 2f,
                        4f,
                        MODULE_ROW_HEIGHT - 4f,
                        2f,
                        ACCENT_COLOR
                    )
                }
            }
        }
    }

    private fun drawModulePanel(mouseX: Int, mouseY: Int) {
        val x = guiX + CATEGORY_PANEL_WIDTH + 1f
        val y = guiY + TITLE_BAR_HEIGHT
        val w = MODULE_PANEL_WIDTH
        val h = GUI_HEIGHT - TITLE_BAR_HEIGHT

//        val headerBgColor = Color(
//            TITLE_BAR_COLOR.red,
//            TITLE_BAR_COLOR.green,
//            TITLE_BAR_COLOR.blue,
//            100
//        )
//        NanoVGUtils.drawRect(x, y, w, 22f, headerBgColor)
//
//        val headerText = if (searchActive && searchQuery.isNotEmpty()) {
//            "Search Results"
//        } else {
//            getCategoryDisplayName(currentCategory)
//        }
//
//        moduleFont.drawString(
//            headerText,
//            x + 10f,
//            y + 6f,
//            TEXT_COLOR.rgb
//        )

        val modules = getFilteredModules()
        val totalModHeight = modules.size * (MODULE_ROW_HEIGHT + MODULE_SPACING)
        val minScroll = -(totalModHeight - (h - 0f)).coerceAtLeast(0f)
        val maxScroll = 0f

        if (MathUtils.isHover(mouseX, mouseY, x, y + 0f, x + w, y + h)) {
            val dWheel = Mouse.getDWheel() * scrollSpeed
            moduleScrollTarget = (moduleScrollTarget + dWheel).coerceIn(minScroll, maxScroll)
        }
        NanoVGUtils.scissor(x, y + 0f, w, h - 0f) {
            modules.forEachIndexed { i, mod ->
                val yPos = y + 0f + MODULE_TOP_MARGIN + moduleScroll + i * (MODULE_ROW_HEIGHT + MODULE_SPACING)
                if (yPos + MODULE_ROW_HEIGHT < y + 0f || yPos > y + h) return@forEachIndexed

                ensureModuleAnimationExists(mod)
                val hovered = MathUtils.isHover(mouseX, mouseY, x, yPos, x + w, yPos + MODULE_ROW_HEIGHT)
                val selected = (mod == currentModule)

                // Update module state animation
                if (mod.state) {
                    moduleStateAnimations[mod] = min(moduleStateAnimations[mod]!! + 0.05f, 1f)
                } else {
                    moduleStateAnimations[mod] = max(moduleStateAnimations[mod]!! - 0.05f, 0f)
                }

                val stateAnim = moduleStateAnimations[mod]!!

                val baseBgColor = when {
                    selected -> ACCENT_COLOR
                    hovered -> HOVER_COLOR
                    else -> HIGHLIGHT_COLOR
                }

                val bgColor = Color(
                    baseBgColor.red,
                    baseBgColor.green,
                    baseBgColor.blue,
                    if (selected) 60 else 40
                )

                NanoVGUtils.rect(x + 5f, yPos, w - 10f, MODULE_ROW_HEIGHT, 4f, bgColor)

                if (stateAnim > 0) {
                    NanoVGUtils.rect(
                        x + 7f,
                        yPos + 2f,
                        4f,
                        MODULE_ROW_HEIGHT - 4f,
                        2f,
                        Color(
                            BOOL_ON_COLOR.red,
                            BOOL_ON_COLOR.green,
                            BOOL_ON_COLOR.blue,
                            (255 * stateAnim).toInt()
                        )
                    )
                }

                val r = TEXT_COLOR.red + ((BOOL_ON_COLOR.red - TEXT_COLOR.red) * stateAnim).toInt()
                val g = TEXT_COLOR.green + ((BOOL_ON_COLOR.green - TEXT_COLOR.green) * stateAnim).toInt()
                val b = TEXT_COLOR.blue + ((BOOL_ON_COLOR.blue - TEXT_COLOR.blue) * stateAnim).toInt()
                val textColor = Color(r, g, b)

                moduleFont.string(
                    mod.displayName,
                    x + 17f,
                    yPos + (MODULE_ROW_HEIGHT - moduleFont.height) / 2f,
                    textColor
                )

                if (mod.displayableValues.isNotEmpty()) {
                    val settingsIconColor = if (selected) ACCENT_COLOR else Color(SUBTITLE_COLOR.red, SUBTITLE_COLOR.green, SUBTITLE_COLOR.blue, (SUBTITLE_COLOR.alpha * 0.7f).toInt())
                    icon12Font.string(
                        SETTINGS_ICON,
                        x + w - 25f,
                        yPos + (MODULE_ROW_HEIGHT - icon12Font.height) / 2f,
                        settingsIconColor
                    )
                }
            }
        }
    }

    private fun drawSettingsPanel(module: Module, mouseX: Int, mouseY: Int) {
        val x = guiX + CATEGORY_PANEL_WIDTH + MODULE_PANEL_WIDTH + 1f
        val y = guiY + TITLE_BAR_HEIGHT
        val w = GUI_WIDTH - (CATEGORY_PANEL_WIDTH + MODULE_PANEL_WIDTH + 2f)
        val h = GUI_HEIGHT - TITLE_BAR_HEIGHT

        val values = module.displayableValues
        if (values.isEmpty()) {
            val noSettingsY = y + 70f
            val noSettingsMessage = "No settings available"

            NanoVGUtils.rect(
                x + (w - icon24Font.width(SETTINGS_ICON)) / 2f - 20f,
                noSettingsY - 10f,
                40f,
                40f,
                color = Color(HIGHLIGHT_COLOR.red, HIGHLIGHT_COLOR.green, HIGHLIGHT_COLOR.blue, 120)
            )

            icon24Font.string(
                SETTINGS_ICON,
                x + (w - icon24Font.width(SETTINGS_ICON)) / 2f,
                noSettingsY,
                SUBTITLE_COLOR
            )

            settingFont.string(
                noSettingsMessage,
                x + (w - settingFont.width(noSettingsMessage)) / 2f,
                noSettingsY + 40f,
                SUBTITLE_COLOR
            )

            return
        }

        val contentY = y
        val contentHeight = h
        var offsetY = 8f
        val totalValHeight = values.sumOf {
            when (it) {
                is ListValue -> if (listValueDropdownOpen.getOrDefault(it, false))
                    (it.values.size + 1) * SETTING_ROW_HEIGHT + SETTING_SPACING
                else
                    SETTING_ROW_HEIGHT + SETTING_SPACING
                else -> SETTING_ROW_HEIGHT + SETTING_SPACING
            }.toInt()
        }

        val minScroll = -(totalValHeight - contentHeight).coerceAtLeast(0f)
        val maxScroll = 0f

        if (MathUtils.isHover(mouseX, mouseY, x, contentY, x + w, y + h)) {
            val dWheel = Mouse.getDWheel() * scrollSpeed
            valueScrollTarget = (valueScrollTarget + dWheel).coerceIn(minScroll, maxScroll)
        }

        NanoVGUtils.scissor(x, contentY, w, contentHeight) {
            values.forEach { v ->
                val yPos = contentY + offsetY + valueScroll

                if (yPos + SETTING_ROW_HEIGHT < contentY || yPos > contentY + contentHeight) {
                    // Skip rendering for items not in view
                    offsetY += when (v) {
                        is ListValue -> if (listValueDropdownOpen.getOrDefault(v, false))
                            (v.values.size + 1) * SETTING_ROW_HEIGHT + SETTING_SPACING
                        else
                            SETTING_ROW_HEIGHT + SETTING_SPACING
                        else -> SETTING_ROW_HEIGHT + SETTING_SPACING
                    }
                    return@forEach
                }

                NanoVGUtils.rect(
                    x + 8f, yPos, w - 16f, SETTING_ROW_HEIGHT,
                    4f, HIGHLIGHT_COLOR
                )

                settingFont.string(
                    v.name,
                    x + 14f,
                    yPos + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
                    TEXT_COLOR
                )

                when (v) {
                    is BoolValue -> drawBoolValue(v, x + w - 62f, yPos)
                    is IntegerValue -> drawIntValue(v, x + w - 110f, yPos, mouseX, mouseY)
                    is FloatValue -> drawFloatValue(v, x + w - 110f, yPos, mouseX, mouseY)
                    is ListValue -> drawListValue(v, x + w - 110f, yPos, mouseX, mouseY)
                }

                offsetY += when (v) {
                    is ListValue -> if (listValueDropdownOpen.getOrDefault(v, false))
                        (v.values.size + 1) * SETTING_ROW_HEIGHT + SETTING_SPACING
                    else
                        SETTING_ROW_HEIGHT + SETTING_SPACING
                    else -> SETTING_ROW_HEIGHT + SETTING_SPACING
                }
            }
        }
    }


    private fun drawBoolValue(value: BoolValue, x: Float, y: Float) {
        ensureBoolAnimationExists(value)

        val targetValue = if (value.value) 1f else 0f
        val currentValue = booleanValueAnimations[value]!!
        val animationSpeed = 0.01f

        val newValue = if (targetValue > currentValue) {
            min(currentValue + animationSpeed, targetValue)
        } else {
            max(currentValue - animationSpeed, targetValue)
        }
        booleanValueAnimations[value] = newValue

        val easedValue = if (targetValue > 0.5f) {
            when (newValue) {
                0.0f -> 0.0f
                1.0f -> 1.0f
                else -> (2.0.pow(-10.0 * newValue) * sin((newValue * 10.0 - 0.75) * 2.0943951023931953) * 0.5 + 1.0).toFloat()
            }
        } else {
            1f - (if (1f - newValue == 0.0f) 0.0f else if (1f - newValue == 1.0f) 1.0f
            else (2.0.pow(-10.0 * (1f - newValue)) * sin(((1f - newValue) * 10.0 - 0.75) * 2.0943951023931953) * 0.5 + 1.0)).toFloat()
        }

        val toggleWidth = 25f
        val toggleHeight = SETTING_ROW_HEIGHT - 6f
        val knobSize = toggleHeight - 2f

        val r = BOOL_OFF_COLOR.red + ((BOOL_ON_COLOR.red - BOOL_OFF_COLOR.red) * easedValue).toInt()
        val g = BOOL_OFF_COLOR.green + ((BOOL_ON_COLOR.green - BOOL_OFF_COLOR.green) * easedValue).toInt()
        val b = BOOL_OFF_COLOR.blue + ((BOOL_ON_COLOR.blue - BOOL_OFF_COLOR.blue) * easedValue).toInt()
        val bgColor = Color(r, g, b)

        NanoVGUtils.rect(
            x + 20f, y + 3f, toggleWidth, toggleHeight,
            toggleHeight / 2f, bgColor
        )

        val knobX = x + 21f + (toggleWidth - knobSize - 2f) * easedValue
        NanoVGUtils.circle(
            knobX + knobSize / 2f,
            y + 3f + toggleHeight / 2f,
            knobSize / 2f,
            Color.WHITE
        )
    }

    private fun drawIntValue(value: IntegerValue, x: Float, y: Float, mouseX: Int, mouseY: Int) {
        drawSlider(
            current = value.value.toFloat(),
            min = value.minRange.toFloat(),
            max = value.maxRange.toFloat(),
            x = x, y = y,
            mouseX = mouseX, mouseY = mouseY,
            valueObject = value
        ) { newVal -> value.set(newVal.toInt()) }
    }

    private fun drawFloatValue(value: FloatValue, x: Float, y: Float, mouseX: Int, mouseY: Int) {
        drawSlider(
            current = value.value,
            min = value.minRange,
            max = value.maxRange,
            x = x, y = y,
            mouseX = mouseX, mouseY = mouseY,
            valueObject = value
        ) { newVal -> value.set(newVal) }
    }

    private fun drawListValue(value: ListValue, x: Float, y: Float, mouseX: Int, mouseY: Int) {
        val isOpen = listValueDropdownOpen.getOrDefault(value, false)
        val text = value.value
        val textWidth = settingFont.width(text)
        val boxWidth = textWidth + 30f

        NanoVGUtils.rect(
            x, y + 1f, boxWidth, SETTING_ROW_HEIGHT - 2f,
            4f, HIGHLIGHT_COLOR
        )

        settingFont.string(
            text,
            x + 10f,
            y + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
            TEXT_COLOR
        )

        val arrowChar = if (isOpen) "^" else "v"
        settingFont.string(
            arrowChar,
            x + boxWidth - 15f,
            y + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
            TEXT_COLOR
        )

        if (MathUtils.isHover(mouseX, mouseY, x, y, x + boxWidth, y + SETTING_ROW_HEIGHT) && Mouse.isButtonDown(0)) {
            if (!listValueClickMap.getOrDefault(value, false)) {
                listValueDropdownOpen[value] = !isOpen
                listValueClickMap[value] = true
            }
        } else {
            listValueClickMap[value] = false
        }

        if (isOpen) {
            value.values.forEachIndexed { index, option ->
                val optionY = y + (index + 1) * SETTING_ROW_HEIGHT
                val optionHovered = MathUtils.isHover(mouseX, mouseY, x, optionY, x + boxWidth, optionY + SETTING_ROW_HEIGHT)
                val optionBgColor = if (optionHovered) HOVER_COLOR else HIGHLIGHT_COLOR
                val optionSelected = option == value.value

                NanoVGUtils.rect(
                    x, optionY, boxWidth, SETTING_ROW_HEIGHT,
                    if (index == value.values.size - 1) 4f else 0f,
                    optionBgColor
                )

                if (optionSelected) {
                    NanoVGUtils.rect(
                        x + 2f, optionY + 2f,
                        4f, SETTING_ROW_HEIGHT - 4f,
                        2f, ACCENT_COLOR
                    )
                }

                settingFont.string(
                    option,
                    x + 10f,
                    optionY + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
                    if (optionSelected) ACCENT_COLOR else TEXT_COLOR
                )

                if (optionHovered && Mouse.isButtonDown(0)) {
                    value.set(option)
                    listValueDropdownOpen[value] = false
                }
            }
        }
    }

    private fun drawSlider(
        current: Float,
        min: Float,
        max: Float,
        x: Float,
        y: Float,
        mouseX: Int,
        mouseY: Int,
        valueObject: Any,
        setter: (Float) -> Unit
    ) {
        val sliderWidth = 75f
        val sliderHeight = 6f
        val sliderY = y + (SETTING_ROW_HEIGHT - sliderHeight) / 2f
        val progress = (current - min) / (max - min).coerceAtLeast(0.00001f)

        if (!sliderHandleAnimations.containsKey(valueObject)) {
            sliderHandleAnimations[valueObject] = 0f
        }

        val isHovered = MathUtils.isHover(
            mouseX, mouseY,
            x - 5f, y,
            x + sliderWidth + 5f, y + SETTING_ROW_HEIGHT
        )

        if (isHovered || draggingSliderValue == valueObject) {
            sliderHandleAnimations[valueObject] = min(sliderHandleAnimations[valueObject]!! + 0.1f, 1f)
        } else {
            sliderHandleAnimations[valueObject] = max(sliderHandleAnimations[valueObject]!! - 0.1f, 0f)
        }

        val handleAnim = sliderHandleAnimations[valueObject]!!

        NanoVGUtils.rect(
            x,
            sliderY,
            sliderWidth,
            sliderHeight,
            sliderHeight / 2f,
            SLIDER_BG_COLOR
        )

        NanoVGUtils.rect(
            x,
            sliderY,
            sliderWidth * progress,
            sliderHeight,
            sliderHeight / 2f,
            SLIDER_PROGRESS_COLOR
        )

        val handleSize = 10f + 2f * handleAnim
        val handleX = x + (sliderWidth * progress) - (handleSize / 2f)
        val handleY = sliderY + (sliderHeight / 2f) - (handleSize / 2f)

        NanoVGUtils.circle(
            handleX + handleSize / 2f,
            handleY + handleSize / 2f,
            handleSize / 2f,
            Color.WHITE
        )

        val display = when (editingValue) {
            valueObject -> editingText + (if ((System.currentTimeMillis() / 500) % 2 == 0L) "|" else "")
            else -> when (valueObject) {
                is IntegerValue -> "${valueObject.value}"
                is FloatValue -> String.format("%.1f", current)
                else -> String.format("%.1f", current)
            }
        }

        val isEditingThisValue = editingValue == valueObject
        val textDisplayBgColor = if (isEditingThisValue) {
            ACCENT_COLOR.darker()
        } else {
            val textAreaHovered = MathUtils.isHover(
                mouseX, mouseY,
                x + sliderWidth + 5f, y,
                x + sliderWidth + 50f, y + SETTING_ROW_HEIGHT
            )
            if (textAreaHovered) HOVER_COLOR else TRANSPARENT
        }

        if (isEditingThisValue || textDisplayBgColor != TRANSPARENT) {
            val textWidth = settingFont.width(display) + 10f
            NanoVGUtils.rect(
                x + sliderWidth + 5f, y + 1f,
                textWidth, SETTING_ROW_HEIGHT - 2f,
                4f, textDisplayBgColor
            )
        }

        settingFont.string(
            display,
            x + sliderWidth + 10f,
            y + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
            if (isEditingThisValue) TEXT_COLOR.brighter() else TEXT_COLOR
        )

        if (draggingSliderValue == valueObject && Mouse.isButtonDown(0)) {
            if (mouseX >= x && mouseX <= x + sliderWidth) {
                val newVal = ((mouseX - x) / sliderWidth) * (max - min) + min
                setter(newVal.coerceIn(min, max))
            }
        }

        val textAreaHovered = MathUtils.isHover(
            mouseX, mouseY,
            x + sliderWidth + 5f, y,
            x + sliderWidth + 50f, y + SETTING_ROW_HEIGHT
        )

        if (!valueEditMap.containsKey(valueObject)) {
            valueEditMap[valueObject] = false
        }

        if (textAreaHovered && Mouse.isButtonDown(0)) {
            if (!valueEditMap[valueObject]!!) {
                if (editingValue != valueObject) {
                    editingValue = valueObject
                    editingText = when (valueObject) {
                        is IntegerValue -> "${valueObject.value}"
                        is FloatValue -> String.format("%.1f", valueObject.value)
                        else -> ""
                    }
                    editingCursorBlink = System.currentTimeMillis()
                }
                valueEditMap[valueObject] = true
            }
        } else {
            valueEditMap[valueObject] = false
        }
    }


    private fun updateScrollAnimations() {
        if (categoryScroll != categoryScrollTarget) {
            val diff = categoryScrollTarget - categoryScroll
            categoryScroll += diff * SCROLL_ANIMATION_SPEED
            if (abs(diff) < 0.1f) categoryScroll = categoryScrollTarget
        }

        if (moduleScroll != moduleScrollTarget) {
            val diff = moduleScrollTarget - moduleScroll
            moduleScroll += diff * SCROLL_ANIMATION_SPEED
            if (abs(diff) < 0.1f) moduleScroll = moduleScrollTarget
        }

        if (valueScroll != valueScrollTarget) {
            val diff = valueScrollTarget - valueScroll
            valueScroll += diff * SCROLL_ANIMATION_SPEED
            if (abs(diff) < 0.1f) valueScroll = valueScrollTarget
        }
    }

    private fun updateAnimations() {
        updateScrollAnimations()

        BloomClient.moduleManager.modules.forEach { mod ->
            ensureModuleAnimationExists(mod)
            if (mod.state) {
                moduleStateAnimations[mod] = min(moduleStateAnimations[mod]!! + 0.05f, 1f)
            } else {
                moduleStateAnimations[mod] = max(moduleStateAnimations[mod]!! - 0.05f, 0f)
            }
        }

        for ((value, _) in booleanValueAnimations) {
            if (value.value) {
                booleanValueAnimations[value] = min(booleanValueAnimations[value]!! + 0.05f, 1f)
            } else {
                booleanValueAnimations[value] = max(booleanValueAnimations[value]!! - 0.05f, 0f)
            }
        }
    }

    override fun mouseClicked(mouseX: Int, mouseY: Int, mouseButton: Int) {
        if (editingValue != null) {
            val valueObject = editingValue!!
            try {
                when (valueObject) {
                    is IntegerValue -> {
                        val newValue = editingText.toIntOrNull() ?: return
                        valueObject.set(newValue.coerceIn(valueObject.minRange, valueObject.maxRange))
                    }
                    is FloatValue -> {
                        val newValue = editingText.toFloatOrNull() ?: return
                        valueObject.set(newValue.coerceIn(valueObject.minRange, valueObject.maxRange))
                    }
                }
            } catch (e: Exception) {
                // Ignore parsing errors
            }
            editingValue = null
        }

        if (MathUtils.isHover(mouseX, mouseY, guiX, guiY, guiX + GUI_WIDTH, guiY + TITLE_BAR_HEIGHT) &&
            mouseButton == 0
        ) {
            draggingGUI = true
            dragOffsetX = mouseX - guiX
            dragOffsetY = mouseY - guiY
            return
        }

        val catX = guiX
        val catY = guiY + TITLE_BAR_HEIGHT + CATEGORY_TOP_MARGIN
        val catW = CATEGORY_PANEL_WIDTH

        if (!searchActive || searchQuery.isEmpty()) {
            ModuleCategory.values().forEachIndexed { i, cat ->
                val yPos = catY + categoryScroll + i * (MODULE_ROW_HEIGHT + CAT_SPACING)
                if (MathUtils.isHover(mouseX, mouseY, catX, yPos, catX + catW, yPos + MODULE_ROW_HEIGHT)) {
                    if (currentCategory != cat) {
                        currentCategory = cat
                        currentModule = null
                        moduleScrollTarget = 0f
                        moduleScroll = 0f
                        valueScrollTarget = 0f
                        valueScroll = 0f
                    }
                    return
                }
            }
        }

        val modX = guiX + CATEGORY_PANEL_WIDTH + 1f
        val modY = guiY + TITLE_BAR_HEIGHT
        val modules = getFilteredModules()
        modules.forEachIndexed { i, m ->
            val yPos = modY + MODULE_TOP_MARGIN + moduleScroll + i * (MODULE_ROW_HEIGHT + MODULE_SPACING)
            if (MathUtils.isHover(mouseX, mouseY, modX, yPos, modX + MODULE_PANEL_WIDTH, yPos + MODULE_ROW_HEIGHT)) {
                when (mouseButton) {
                    0 -> m.toggle()  // left-click: toggle
                    1 -> {  // right-click: open settings
                        currentModule = if (currentModule == m) null else m
                        valueScroll = 0f
                    }
                }
                return
            }
        }

        currentModule?.let { module ->
            val values = module.displayableValues
            val settingsX = guiX + CATEGORY_PANEL_WIDTH + MODULE_PANEL_WIDTH + 1f
            val settingsY = guiY + TITLE_BAR_HEIGHT

            var offsetY = 8f

            values.forEach { v ->
                val yPos = settingsY + offsetY + valueScroll

                when (v) {
                    is BoolValue -> {
                        val toggleX = settingsX + (GUI_WIDTH - CATEGORY_PANEL_WIDTH - MODULE_PANEL_WIDTH - 2f) - 62f
                        if (MathUtils.isHover(mouseX, mouseY, toggleX, yPos, toggleX + 40f, yPos + SETTING_ROW_HEIGHT) && mouseButton == 0) {
                            v.set(!v.value)
                            return
                        }
                    }
                    is IntegerValue, is FloatValue -> {
                        val sliderX = settingsX + (GUI_WIDTH - CATEGORY_PANEL_WIDTH - MODULE_PANEL_WIDTH - 2f) - 110f
                        if (MathUtils.isHover(mouseX, mouseY, sliderX, yPos, sliderX + 100f, yPos + SETTING_ROW_HEIGHT) && mouseButton == 0) {
                            draggingSliderValue = v
                            return
                        }
                    }
                }

                offsetY += when (v) {
                    is ListValue -> if (listValueDropdownOpen.getOrDefault(v, false))
                        (v.values.size + 1) * SETTING_ROW_HEIGHT + SETTING_SPACING
                    else
                        SETTING_ROW_HEIGHT + SETTING_SPACING
                    else -> SETTING_ROW_HEIGHT + SETTING_SPACING
                }
            }
        }

        if (mouseButton == 0) draggingSliderValue = null
    }

    override fun mouseReleased(mouseX: Int, mouseY: Int, state: Int) {
        draggingGUI = false
        draggingSliderValue = null
    }

    override fun keyTyped(typedChar: Char, keyCode: Int) {
        if (keyCode == Keyboard.KEY_ESCAPE) {
            if (editingValue != null) {
                editingValue = null
                return
            }
            if (searchActive && searchQuery.isNotEmpty()) {
                searchQuery = ""
                moduleScroll = 0f
            } else {
                mc.displayGuiScreen(null)
            }
            return
        }

        if (editingValue != null) {
            when (keyCode) {
                Keyboard.KEY_BACK -> {
                    if (editingText.isNotEmpty()) {
                        editingText = editingText.substring(0, editingText.length - 1)
                    }
                }
                Keyboard.KEY_RETURN, Keyboard.KEY_NUMPADENTER -> {
                    val valueObject = editingValue!!
                    try {
                        when (valueObject) {
                            is IntegerValue -> {
                                val newValue = editingText.toIntOrNull() ?: return
                                valueObject.set(newValue.coerceIn(valueObject.minRange, valueObject.maxRange))
                            }
                            is FloatValue -> {
                                val newValue = editingText.toFloatOrNull() ?: return
                                valueObject.set(newValue.coerceIn(valueObject.minRange, valueObject.maxRange))
                            }
                        }
                    } catch (e: Exception) {
                        // Ignore parsing errors
                    }
                    editingValue = null
                }
                else -> {
                    if (typedChar.isDigit() || typedChar == '.' || typedChar == '-') {
                        if (editingValue is IntegerValue && typedChar == '.') return
                        if (typedChar == '.' && editingText.contains('.')) return
                        if (typedChar == '-' && editingText.isNotEmpty()) return

                        editingText += typedChar
                    }
                }
            }
            editingCursorBlink = System.currentTimeMillis()
            return
        }

        // dit me
        if (searchActive) {
            when (keyCode) {
                Keyboard.KEY_BACK -> {
                    if (searchQuery.isNotEmpty()) {
                        searchQuery = searchQuery.substring(0, searchQuery.length - 1)
                        moduleScrollTarget = 0f
                        moduleScroll = 0f
                    }
                }
                else -> {
                    if (typedChar.isLetterOrDigit() || typedChar.isWhitespace()) {
                        searchQuery += typedChar
                        moduleScrollTarget = 0f
                        moduleScroll = 0f
                        currentModule = null
                    }
                }
            }
        }
    }

    override fun doesGuiPauseGame() = false

}
