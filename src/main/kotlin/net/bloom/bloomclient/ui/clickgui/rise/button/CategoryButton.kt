package net.bloom.bloomclient.ui.clickgui.rise.button

import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.ui.clickgui.rise.RiseClickGUI
import net.bloom.bloomclient.utils.MathUtils.mouseOver
import java.awt.Color

/**
 * Category button
 **/
class CategoryButton(val category: ModuleCategory) : Button() {

    init {
        scale.x = 80F
        scale.y = 15F
    }

    override fun render(mouseX: Float, mouseY: Float) {
        f10.centeredString(category.categoryName, pos.x + scale.x / 2, pos.y + (scale.y - f10.height) / 2, Color(250, 250, 250, 200))
    }

    override fun click(mouseX: Float, mouseY: Float, button: Int) {
        if (mouseOver(mouseX, mouseY, pos.x, pos.y, scale.x, scale.y) && button == 0)
            RiseClickGUI.switch(category)
    }

    override fun release(mouseX: Float, mouseY: Float, button: Int) {

    }

}