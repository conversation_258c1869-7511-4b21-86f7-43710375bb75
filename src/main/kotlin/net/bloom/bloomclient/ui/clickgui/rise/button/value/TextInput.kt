package net.bloom.bloomclient.ui.clickgui.rise.button.value

import net.bloom.bloomclient.value.values.TextValue
import java.awt.Color

class TextInput(value: TextValue) : ValueButton(value) {
    override fun render(mouseX: Float, mouseY: Float) {
        super.render(mouseX, mouseY)

        if (value is TextValue) {
            f8.string(value.name, pos.x, pos.y, Color(200, 200, 200, 250))
        }
    }
}