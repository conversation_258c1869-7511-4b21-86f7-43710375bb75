package net.bloom.bloomclient.font.renderer

import net.bloom.bloomclient.utils.io.FileUtils
import net.bloom.bloomclient.utils.render.NanoVGUtils
import net.bloom.bloomclient.utils.render.NanoVGUtils.toNVGColor
import net.minecraft.client.MinecraftInstance
import net.minecraft.client.gui.Side
import org.lwjgl.nanovg.NanoVG
import java.awt.Color
import java.nio.ByteBuffer

/**
 * A font renderer using NanoVG
 * <AUTHOR> Blend Client
 */
class NanoVGFontRenderer(
    val name: String, val height: Int, val path: String, val setup: Boolean = true
): MinecraftInstance() {
    val buffer: ByteBuffer = FileUtils.getFontBuffer(path)
    val context = NanoVGUtils.context

    init {
        if (setup) setupFont()
    }

    private fun setupFont() {
        val fontId = NanoVG.nvgCreateFontMem(context, name, buffer, false)
        if (fontId == -1) throw IllegalStateException("Failed to load font: $name")
    }

    fun string(text: String?, x: Float, y: Float, color: Color, side: Side = Side.DEFAULT, dropShadow: Boolean = false): Float {
        if (context == 0L || text == null) return 0F

        NanoVG.nvgBeginPath(context)

        NanoVG.nvgFillColor(context, color.toNVGColor())
        NanoVG.nvgFontFace(context, name)
        NanoVG.nvgFontSize(context, height.toFloat())
        NanoVG.nvgTextAlign(context, NanoVGUtils.getNanoVGSide(side))

        return NanoVG.nvgText(context, x, y, text)
    }

    fun centeredString(text: String?, x: Float, y: Float, color: Color, dropShadow: Boolean = false): Float {
        return string(text, x, y, color, Side(Side.Horizontal.MIDDLE, Side.Vertical.TOP), dropShadow)
    }

    fun width(text: String?): Float {
        text ?: return 0F

        val bounds = floatArrayOf(0F, 0F, 0F, 0F)

        val context = NanoVGUtils.context
        NanoVG.nvgFontFace(context, name)
        NanoVG.nvgFontSize(context, height.toFloat())
        NanoVG.nvgTextBounds(context, 0F, 0F, text, bounds)

        val left = bounds[0]
        val right = bounds[2]

        return (right - left)
    }
}
