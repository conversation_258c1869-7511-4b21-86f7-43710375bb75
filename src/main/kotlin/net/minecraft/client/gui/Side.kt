package net.minecraft.client.gui

class Side(var horizontal: Horizontal, var vertical: Vertical) {

    enum class Horizontal(val sideName: String) {
        LEFT("Left"),
        MIDDLE("Middle"),
        RIGHT("Right")
    }

    enum class Vertical(val sideName: String) {
        TOP("Top"),
        MIDDLE("Middle"),
        BOTTOM("Bottom")
    }

    companion object {
        val DEFAULT = Side(Horizontal.LEFT, Vertical.TOP)
    }

    fun calculatePosition(x: Float, y: Float, width: Float, height: Float): Pair<Float, Float> {
        val adjustedX = when (horizontal) {
            Horizontal.LEFT -> x
            Horizontal.MIDDLE -> x - (width / 2)
            Horizontal.RIGHT -> x - width
        }

        val adjustedY = when (vertical) {
            Vertical.TOP -> y
            Vertical.MIDDLE -> y - (height / 2)
            Vertical.BOTTOM -> y - height
        }

        return Pair(adjustedX, adjustedY)
    }
}